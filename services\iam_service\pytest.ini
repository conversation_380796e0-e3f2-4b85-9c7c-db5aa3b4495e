[tool:pytest]
# pytest配置文件

# 测试目录
testpaths = tests

# 测试文件模式
python_files = test_*.py *_test.py

# 测试类模式
python_classes = Test*

# 测试函数模式
python_functions = test_*

# 标记定义
markers =
    unit: 单元测试
    integration: 集成测试
    performance: 性能测试
    security: 安全测试
    slow: 慢速测试
    redis: 需要Redis的测试
    database: 需要数据库的测试

# 异步测试支持
asyncio_mode = auto

# 输出配置
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes
    --durations=10
    --cov=security
    --cov=middleware
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-fail-under=80

# 最小版本要求
minversion = 6.0

# 测试发现
norecursedirs = .git .tox dist build *.egg

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:jwt.*
