from dataclasses import dataclass
from threading import RLock
from typing import Any, Dict, List, Tuple, Type

from pydantic import BaseModel

from commonlib.configs.basic_configs import PersistenceConfig
from commonlib.configs.services.connector_priority.connector_priority_config import (
    ConnectionPriority,
)
from commonlib.core.tsif_logging import app_logger
from commonlib.storages.base import BaseConnector


@dataclass
class ConnectorSpec:
    db_type: str
    connector_class: Type[BaseConnector]
    priority: ConnectionPriority


class ConnectionManager:
    """依赖注入模式下的连接管理器"""

    def __init__(self, config: PersistenceConfig, connector_registry: Dict[str, dict]):
        self._config = config
        self._registry: Dict[str, ConnectorSpec] = {}
        self._connectors: Dict[str, BaseConnector] = {}
        self._lock = RLock()
        for db_type, item in connector_registry.items():
            self._registry[db_type] = ConnectorSpec(
                db_type=db_type,
                connector_class=item["class"],
                priority=item["priority"],
            )

    async def connect_all(self):
        """
        按优先级建立所有连接

        Raises:
            ConnectionInitError: 当任何连接初始化失败时抛出
        """
        for db_type, spec in self.get_sorted_connect_order():
            await self.connect_single(db_type, spec)

    async def close_all(self):
        """
        按反向优先级关闭所有连接
        """
        for db_type, _ in self.get_sorted_shutdown_order():
            await self.close_single(db_type)

    async def connect_single(self, db_type: str, spec: ConnectorSpec):
        """
        连接单个数据源

        Args:
            db_type: 数据库类型标识
            spec: 连接器规格

        Raises:
            ConnectionInitError: 连接初始化失败时抛出
        """
        await self.close_single(db_type)

        try:
            instance: BaseConnector = spec.connector_class()
            db_config = getattr(self._config, db_type)
            if isinstance(db_config, BaseModel):
                db_config = db_config.model_dump()
            instance.load_config(db_config)
            await instance.start()
            self._connectors[db_type] = instance
            app_logger.info(
                f"[ConnectionManager] Connected successfully,db_type :{db_type}, "
                f"connector_class:{spec.connector_class.__name__}"
            )
        except Exception as e:
            error_msg = (f"[ConnectionManager] Connection failed, "
                         f"db_type:{db_type}, error:{str(e)}")
            app_logger.error(error_msg, exception=True)
            raise ValueError(error_msg)

    async def close_single(self, db_type: str):
        """
        关闭单个数据源连接

        Args:
            db_type: 数据库类型标识
        """
        if db_type not in self._connectors:
            return
        try:
            await self._connectors[db_type].stop(fource_stop=True)
            app_logger.info(f"[ConnectionManager] Closed successfully, db_type:{db_type}")
        except Exception as e:
            app_logger.error(
                f"[ConnectionManager] Close failed, db_type:{db_type}, error:{str(e)}",
                exception=True,
            )
        self._connectors.pop(db_type, None)

    async def refresh_connector(self, db_type: str):
        """
        热重载指定连接器（用于配置动态更新）

        Args:
            db_type: 数据库类型标识

        Raises:
            ValueError: 连接器类型不存在时抛出
            ConnectionInitError: 重新连接失败时抛出
        """
        if db_type not in self._registry:
            raise ValueError(f"Connector {db_type} not registered")

        spec = self._registry[db_type]
        app_logger.info(f"[ConnectionManager] Refreshing connector, db_type:{db_type}")
        await self.connect_single(db_type, spec)

    def get_connector(self, db_type: str) -> BaseConnector:
        """
        获取指定类型的连接器实例

        Args:
            db_type: 数据库类型标识

        Returns:
            BaseConnector: 连接器实例

        Raises:
            ValueError: 连接器未初始化时抛出
        """
        with self._lock:
            if db_type not in self._connectors:
                raise ValueError(f"Connector {db_type} not initialized")
            app_logger.debug(f"[ConnectionManager] Retrieved connector,db_type:{db_type}")
            return self._connectors[db_type]

    def get_status(self) -> Dict[str, Any]:
        """
        获取所有连接器的健康状态

        Returns:
            Dict[str, Any]: 包含各连接器健康状态的字典
        """
        status = {}
        with self._lock:
            for db_type, connector in self._connectors.items():
                try:
                    is_healthy = (
                        connector.is_healthy() if hasattr(connector, "is_healthy") else True
                    )
                    heartbeat_status = (
                        connector.get_heartbeat_status()
                        if hasattr(connector, "get_heartbeat_status")
                        else {}
                    )
                    status[db_type] = {
                        "healthy": is_healthy,
                        "connector_class": connector.__class__.__name__,
                        "heartbeat": heartbeat_status,
                    }
                except Exception as e:
                    app_logger.error(
                        f"[ConnectionManager] Failed to get status, db_type:{db_type}, error:{str(e)}"
                    )
                    status[db_type] = {"healthy": False, "error": str(e)}
        return status

    def get_sorted_connect_order(self) -> List[Tuple[str, ConnectorSpec]]:
        """
        获取按连接优先级排序的连接器列表

        Returns:
            List[Tuple[str, ConnectorSpec]]: 排序后的连接器列表
        """
        return sorted(
            self._registry.items(),
            key=lambda item: item[1].priority.connect_priority,
        )

    def get_sorted_shutdown_order(self) -> List[Tuple[str, ConnectorSpec]]:
        """
        获取按关闭优先级排序的连接器列表

        Returns:
            List[Tuple[str, ConnectorSpec]]: 排序后的连接器列表
        """
        return sorted(
            self._registry.items(),
            key=lambda item: item[1].priority.shutdown_priority,
            reverse=True,
        )

    def validate_all_configs(self):
        """
        验证所有连接器的配置是否有效

        Raises:
            RuntimeError: 配置缺失或验证失败时抛出
        """
        for db_type, spec in self._registry.items():
            db_config = getattr(self._config, db_type, None)
            if not db_config:
                app_logger.error(f"[ConnectionManager] Missing config, db_type:{db_type}")
                raise RuntimeError(f"Missing config for {db_type}")

            try:
                spec.connector_class.validate_config(
                    db_config.model_dump() if isinstance(db_config, BaseModel) else db_config
                )
                app_logger.debug(f"[ConnectionManager] Config validated, db_type:{db_type}")
            except Exception as error:
                app_logger.error(
                    f"[ConnectionManager] Config validation failed, db_type:{db_type}, "
                    f"error:{str(error)}"
                )
                raise
