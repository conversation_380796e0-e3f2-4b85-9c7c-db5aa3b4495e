"""
IAM 服务依赖注入容器

统一管理 IAM 服务的业务服务层依赖注入
"""

from dependency_injector import containers, providers

from commonlib.core.app_config import app_local_config
from commonlib.core.app_connections import app_local_connection_manager
from commonlib.interface.infra_external_services.email_service import EmailService
from commonlib.interface.infra_external_services.sms_service import Mock<PERSON>SProvider, SMSService
from commonlib.interface.infra_external_services.verification_service import VerificationService
from commonlib.interface.infra_redis.cache import RedisLockService
from commonlib.interface.infra_redis.cache.cache_service import RedisCacheService
from commonlib.interface.infra_redis.cache.key_builder import RedisKeyBuilder
from commonlib.storages.persistence.postgres import provide_postgres_session
from commonlib.storages.persistence.redis.repository import RedisRepository

# 导入模型
from domain_common.models.iam_models import (
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>ument,
    KnowledgeBase,
    KnowledgeBaseAccess,
    PasswordHistory,
    Permission,
    PermissionPolicy,
    Role,
    RolePermission,
    SecurityEvent,
    SecurityPolicy,
    SystemConfig,
    Tenant,
    User,
    UserMFA,
    UserPolicy,
    UserRole,
    UserSessionHistory,
    VerificationCode,
)
from domain_common.models.iam_models.initialization.system_bootstrap import SystemBootstrap
from domain_common.security import SecurityUtils
from domain_common.security.cache_manager import UserCacheManager
from domain_common.security.config import security_config_manager

# 导入安全模块
from domain_common.security.jwt_manager import JWTManager
from domain_common.security.session_manager import SessionManager
from services.advanced_security_service import AdvancedSecurityService
from services.audit_service import AuditService
from services.auth_service import AuthService
from services.document_service import DocumentService
from services.knowledge_base_service import KnowledgeBaseService
from services.permission_service import PermissionService
from services.rbac_service import RBACService
from services.role_service import RoleService
from services.system_config_service import SystemConfigService
from services.system_service import SystemService

# 导入业务服务
from services.tenant_service import TenantService
from services.user_service import UserService


class ServiceContainer(containers.DeclarativeContainer):
    """IAM 服务容器 - 管理业务服务层依赖注入"""

    # 外部依赖
    postgres_client = providers.Callable(lambda: app_local_connection_manager.postgres_client)
    redis_client = providers.Callable(lambda: app_local_connection_manager.redis_client)
    session = providers.Resource(
        provide_postgres_session,
        connector=postgres_client,
    )
    # Redis 仓储
    redis_repo = providers.Factory(
        RedisRepository,
        key_prefix=app_local_config.app_name,
        redis_connector=redis_client,
    )
    # redis相关缓存构建（注意，为了避免麻烦，缓存不使用Key的构造）
    redis_cache_service = providers.Factory(
        RedisCacheService,
        redis_repo=redis_repo,
    )
    # redis的key构造
    redis_key_build = providers.Factory(
        RedisKeyBuilder, app_name=app_local_config.app_name, environment="prod"
    )

    # redis相关锁操作构建
    redis_lock_service = providers.Factory(
        RedisLockService, redis_repo=redis_repo, key_builder=redis_key_build
    )

    # 核心业务模型
    user_model = User
    tenant_model = Tenant
    role_model = Role
    permission_model = Permission

    # 关联关系模型
    user_role_model = UserRole
    role_permission_model = RolePermission

    # 认证安全模型
    user_session_history_model = UserSessionHistory
    user_mfa_model = UserMFA
    verification_code_model = VerificationCode
    password_history_model = PasswordHistory

    # 策略模型
    user_policy_model = UserPolicy
    permission_policy_model = PermissionPolicy

    # RAG 相关模型
    knowledge_base_model = KnowledgeBase
    knowledge_base_access_model = KnowledgeBaseAccess
    document_model = Document

    # 任务和审计模型
    batch_task_model = BatchTask
    audit_log_model = AuditLog

    # 系统配置和安全模型
    system_config_model = SystemConfig
    security_policy_model = SecurityPolicy
    security_event_model = SecurityEvent

    # 缓存配置
    cache_key_model = CacheKey
    # 安全模块
    jwt_manager = providers.Factory(
        JWTManager,
        redis_cache_service=redis_cache_service,
        private_key=security_config_manager.jwt_config.private_key,
        public_key=security_config_manager.jwt_config.public_key,
        access_token_expire_minutes=security_config_manager.jwt_config.access_token_expire_minutes,
        refresh_token_expire_days=security_config_manager.jwt_config.refresh_token_expire_days,
        algorithm=security_config_manager.jwt_config.algorithm,
    )

    session_manager = providers.Factory(
        SessionManager,
        redis_cache_service=redis_cache_service,
        session_timeout_minutes=security_config_manager.session_config.session_timeout_minutes,
        max_concurrent_sessions=security_config_manager.session_config.max_concurrent_sessions,
        enable_device_tracking=security_config_manager.session_config.enable_device_tracking,
    )

    cache_manager = providers.Factory(
        UserCacheManager,
        redis_repo=redis_repo,
        config=security_config_manager.cache_config,
    )

    security_utils = providers.Factory(SecurityUtils)

    # 业务服务层
    tenant_service = providers.Factory(
        TenantService,
        session=session,
        redis_repo=redis_repo,
        user_model=user_model,
        tenant_model=tenant_model,
        role_model=role_model,
        permission_model=permission_model,
        user_role_model=user_role_model,
        role_permission_model=role_permission_model,
        audit_log_model=audit_log_model,
    )

    user_service = providers.Factory(
        UserService,
        session=session,
        redis_cache_service=redis_cache_service,
        user_model=user_model,
        tenant_model=tenant_model,
        role_model=role_model,
        permission_model=permission_model,
        user_role_model=user_role_model,
        role_permission_model=role_permission_model,
        audit_log_model=audit_log_model,
        security_utils=security_utils,
    )

    auth_service = providers.Factory(
        AuthService,
        session=session,
        redis_repo=redis_repo,
        user_model=user_model,
        tenant_model=tenant_model,
        role_model=role_model,
        permission_model=permission_model,
        user_role_model=user_role_model,
        role_permission_model=role_permission_model,
        user_session_history_model=user_session_history_model,
        user_mfa_model=user_mfa_model,
        verification_code_model=verification_code_model,
        password_history_model=password_history_model,
        audit_log_model=audit_log_model,
        jwt_manager=jwt_manager,
        session_manager=session_manager,
        security_utils=security_utils,
    )

    role_service = providers.Factory(
        RoleService,
        session=session,
        redis_repo=redis_repo,
        user_model=user_model,
        tenant_model=tenant_model,
        role_model=role_model,
        permission_model=permission_model,
        user_role_model=user_role_model,
        role_permission_model=role_permission_model,
        audit_log_model=audit_log_model,
    )

    permission_service = providers.Factory(
        PermissionService,
        session=session,
        redis_cache_service=redis_cache_service,
        redis_lock_service=redis_lock_service,
        user_model=user_model,
        tenant_model=tenant_model,
        role_model=role_model,
        permission_model=permission_model,
        user_role_model=user_role_model,
        role_permission_model=role_permission_model,
        user_policy_model=user_policy_model,
        permission_policy_model=permission_policy_model,
        audit_log_model=audit_log_model,
    )

    knowledge_base_service = providers.Factory(
        KnowledgeBaseService,
        session=session,
        redis_repo=redis_repo,
        user_model=user_model,
        tenant_model=tenant_model,
        role_model=role_model,
        permission_model=permission_model,
        user_role_model=user_role_model,
        role_permission_model=role_permission_model,
        knowledge_base_model=knowledge_base_model,
        knowledge_base_access_model=knowledge_base_access_model,
        document_model=document_model,
        audit_log_model=audit_log_model,
    )

    document_service = providers.Factory(
        DocumentService,
        session=session,
        redis_repo=redis_repo,
        user_model=user_model,
        tenant_model=tenant_model,
        role_model=role_model,
        permission_model=permission_model,
        user_role_model=user_role_model,
        role_permission_model=role_permission_model,
        knowledge_base_model=knowledge_base_model,
        knowledge_base_access_model=knowledge_base_access_model,
        document_model=document_model,
        audit_log_model=audit_log_model,
        batch_task_model=batch_task_model,
    )

    # 外部服务
    sms_provider = providers.Singleton(MockSMSProvider)

    sms_service = providers.Singleton(
        SMSService,
        provider=sms_provider,
        rate_limit_per_minute=10,
        rate_limit_per_hour=100,
        redis_repo=redis_repo,
    )

    email_service = providers.Singleton(
        EmailService,
        smtp_server="localhost",
        smtp_port=587,
        username="",
        password="",
        use_tls=True,
        sender_name="IAM Service",
    )

    verification_service = providers.Singleton(
        VerificationService,
        redis_repo=redis_repo,
        email_service=email_service,
        sms_service=sms_service,
        default_code_length=6,
        default_expire_minutes=5,
        max_attempts=3,
        rate_limit_minutes=1,
    )

    # 第三阶段新增服务

    # RBAC服务
    rbac_service = providers.Factory(
        RBACService,
        session=session,
        redis_repo=redis_repo,
        user_model=user_model,
        tenant_model=tenant_model,
        role_model=role_model,
        permission_model=permission_model,
        user_role_model=user_role_model,
        role_permission_model=role_permission_model,
        audit_log_model=audit_log_model,
    )

    # 审计服务
    audit_service = providers.Factory(
        AuditService,
        session=session,
        redis_repo=redis_repo,
        user_model=user_model,
        tenant_model=tenant_model,
        audit_log_model=audit_log_model,
    )

    # 系统配置服务
    system_config_service = providers.Factory(
        SystemConfigService,
        session=session,
        redis_repo=redis_repo,
        tenant_model=tenant_model,
        system_config_model=system_config_model,
        audit_log_model=audit_log_model,
        encryption_key=None,
    )

    # 高级安全服务
    advanced_security_service = providers.Factory(
        AdvancedSecurityService,
        session=session,
        redis_repo=redis_repo,
        user_model=user_model,
        tenant_model=tenant_model,
        user_mfa_model=user_mfa_model,
        security_policy_model=security_policy_model,
        security_event_model=security_event_model,
        audit_log_model=audit_log_model,
        security_utils=security_utils,
    )
    system_service = providers.Factory(
        SystemService,
        session=session,
        redis_repo=redis_repo,
        user_model=user_model,
        tenant_model=tenant_model,
        audit_log_model=audit_log_model,
        system_config_model=system_config_model,
        cache_key_model=cache_key_model,
        security_policy_model=security_policy_model,
        security_event_model=security_event_model,
    )

    system_boot_service = providers.Factory(
        SystemBootstrap, session=session, security_utils=security_utils
    )
