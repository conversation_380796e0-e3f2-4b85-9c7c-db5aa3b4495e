"""
短信服务

提供短信发送功能，支持多种短信服务提供商
"""

from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, Optional

from commonlib.exceptions.exceptions import BusinessError


class SMSTemplate:
    """短信模板"""

    # 验证码短信模板
    VERIFICATION_CODE = "【{tenant_name}】您的验证码是{verification_code}，有效期5分钟，请勿泄露。"

    # 登录通知模板
    LOGIN_NOTIFICATION = (
        "【{tenant_name}】您的账户于{login_time}在{device_name}上登录，如非本人操作请及时修改密码。"
    )

    # 密码重置通知模板
    PASSWORD_RESET_NOTIFICATION = "【{tenant_name}】您的密码已重置成功，如非本人操作请联系管理员。"

    # 账户锁定通知模板
    ACCOUNT_LOCKED = "【{tenant_name}】您的账户因多次登录失败已被锁定，请30分钟后重试或联系管理员。"


class BaseSMSProvider(ABC):
    """短信服务提供商基类"""

    @abstractmethod
    async def send_sms(
        self, phone: str, message: str, template_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """发送短信"""

    @abstractmethod
    async def query_balance(self) -> Dict[str, Any]:
        """查询余额"""


class AliyunSMSProvider(BaseSMSProvider):
    """阿里云短信服务提供商"""

    def __init__(
        self,
        access_key_id: str,
        access_key_secret: str,
        sign_name: str,
        region: str = "cn-hangzhou",
    ):
        self.access_key_id = access_key_id
        self.access_key_secret = access_key_secret
        self.sign_name = sign_name
        self.region = region
        self.endpoint = f"https://dysmsapi.{region}.aliyuncs.com"

    async def send_sms(
        self, phone: str, message: str, template_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """发送短信"""
        # TODO: 实现阿里云短信API调用
        # 这里需要实现阿里云短信API的签名和请求逻辑

        # 模拟发送结果
        return {
            "success": True,
            "message_id": f"aliyun_{datetime.now().timestamp()}",
            "provider": "aliyun",
            "sent_at": datetime.now().isoformat(),
        }

    async def query_balance(self) -> Dict[str, Any]:
        """查询余额"""
        # TODO: 实现余额查询
        return {"balance": 1000, "currency": "CNY"}


class TencentSMSProvider(BaseSMSProvider):
    """腾讯云短信服务提供商"""

    def __init__(
        self,
        secret_id: str,
        secret_key: str,
        app_id: str,
        sign_name: str,
        region: str = "ap-beijing",
    ):
        self.secret_id = secret_id
        self.secret_key = secret_key
        self.app_id = app_id
        self.sign_name = sign_name
        self.region = region
        self.endpoint = f"https://sms.{region}.tencentcloudapi.com"

    async def send_sms(
        self, phone: str, message: str, template_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """发送短信"""
        # TODO: 实现腾讯云短信API调用

        # 模拟发送结果
        return {
            "success": True,
            "message_id": f"tencent_{datetime.now().timestamp()}",
            "provider": "tencent",
            "sent_at": datetime.now().isoformat(),
        }

    async def query_balance(self) -> Dict[str, Any]:
        """查询余额"""
        # TODO: 实现余额查询
        return {"balance": 500, "currency": "CNY"}


class MockSMSProvider(BaseSMSProvider):
    """模拟短信服务提供商（用于测试）"""

    def __init__(self):
        self.sent_messages = []

    async def send_sms(
        self, phone: str, message: str, template_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """模拟发送短信"""
        message_id = f"mock_{datetime.now().timestamp()}"

        # 记录发送的消息
        self.sent_messages.append(
            {
                "message_id": message_id,
                "phone": phone,
                "message": message,
                "template_id": template_id,
                "sent_at": datetime.now().isoformat(),
            }
        )

        print(f"模拟短信发送: {phone} - {message}")

        return {
            "success": True,
            "message_id": message_id,
            "provider": "mock",
            "sent_at": datetime.now().isoformat(),
        }

    async def query_balance(self) -> Dict[str, Any]:
        """模拟查询余额"""
        return {"balance": 9999, "currency": "CNY"}

    def get_sent_messages(self) -> list:
        """获取已发送的消息列表"""
        return self.sent_messages.copy()


class SMSService:
    """短信服务类"""

    def __init__(
        self,
        provider: BaseSMSProvider,
        rate_limit_per_minute: int = 10,
        rate_limit_per_hour: int = 100,
        redis_repo=None,
    ):
        self.provider = provider
        self.rate_limit_per_minute = rate_limit_per_minute
        self.rate_limit_per_hour = rate_limit_per_hour
        self.redis_repo = redis_repo

    async def send_verification_code(
        self, phone: str, verification_code: str, tenant_name: str
    ) -> Dict[str, Any]:
        """发送验证码短信"""
        # 检查发送频率限制
        if not await self._check_rate_limit(phone):
            raise BusinessError("短信发送频率过高，请稍后再试")

        # 格式化短信内容
        message = SMSTemplate.VERIFICATION_CODE.format(
            tenant_name=tenant_name, verification_code=verification_code
        )

        # 发送短信
        result = await self.provider.send_sms(phone, message)

        # 记录发送历史
        await self._record_sms_history(phone, "verification_code", result)

        return result

    async def send_login_notification(
        self, phone: str, tenant_name: str, login_time: str, device_name: str
    ) -> Dict[str, Any]:
        """发送登录通知短信"""
        message = SMSTemplate.LOGIN_NOTIFICATION.format(
            tenant_name=tenant_name, login_time=login_time, device_name=device_name
        )

        result = await self.provider.send_sms(phone, message)
        await self._record_sms_history(phone, "login_notification", result)

        return result

    async def send_password_reset_notification(
        self, phone: str, tenant_name: str
    ) -> Dict[str, Any]:
        """发送密码重置通知短信"""
        message = SMSTemplate.PASSWORD_RESET_NOTIFICATION.format(tenant_name=tenant_name)

        result = await self.provider.send_sms(phone, message)
        await self._record_sms_history(phone, "password_reset", result)

        return result

    async def send_account_locked_notification(
        self, phone: str, tenant_name: str
    ) -> Dict[str, Any]:
        """发送账户锁定通知短信"""
        message = SMSTemplate.ACCOUNT_LOCKED.format(tenant_name=tenant_name)

        result = await self.provider.send_sms(phone, message)
        await self._record_sms_history(phone, "account_locked", result)

        return result

    async def send_custom_sms(
        self, phone: str, message: str, template_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """发送自定义短信"""
        # 检查发送频率限制
        if not await self._check_rate_limit(phone):
            raise BusinessError("短信发送频率过高，请稍后再试")

        result = await self.provider.send_sms(phone, message, template_id)
        await self._record_sms_history(phone, "custom", result)

        return result

    async def query_balance(self) -> Dict[str, Any]:
        """查询短信余额"""
        return await self.provider.query_balance()

    async def get_sms_statistics(self, phone: Optional[str] = None) -> Dict[str, Any]:
        """获取短信发送统计"""
        if not self.redis_repo:
            return {"error": "Redis未配置"}

        # TODO: 实现统计查询
        return {"total_sent": 0, "success_rate": 0.0, "last_24h": 0}

    async def _check_rate_limit(self, phone: str) -> bool:
        """检查发送频率限制"""
        if not self.redis_repo:
            return True  # 如果没有Redis，跳过频率限制

        # 检查每分钟限制
        minute_key = f"sms_rate_limit:minute:{phone}:{datetime.now().strftime('%Y%m%d%H%M')}"
        minute_count = await self.redis_repo.get(minute_key) or 0

        if minute_count >= self.rate_limit_per_minute:
            return False

        # 检查每小时限制
        hour_key = f"sms_rate_limit:hour:{phone}:{datetime.now().strftime('%Y%m%d%H')}"
        hour_count = await self.redis_repo.get(hour_key) or 0

        if hour_count >= self.rate_limit_per_hour:
            return False

        # 增加计数
        await self.redis_repo.set(minute_key, minute_count + 1, ttl=60)
        await self.redis_repo.set(hour_key, hour_count + 1, ttl=3600)

        return True

    async def _record_sms_history(self, phone: str, sms_type: str, result: Dict[str, Any]):
        """记录短信发送历史"""
        if not self.redis_repo:
            return

        history_key = f"sms_history:{phone}:{datetime.now().strftime('%Y%m%d')}"
        history_data = {
            "phone": phone,
            "type": sms_type,
            "result": result,
            "timestamp": datetime.now().isoformat(),
        }

        # 将历史记录添加到列表中
        await self.redis_repo.lpush(history_key, history_data)
        await self.redis_repo.expire(history_key, 86400 * 7)  # 保留7天

    def mask_phone(self, phone: str) -> str:
        """手机号脱敏"""
        if len(phone) <= 7:
            return "*" * len(phone)
        return f"{phone[:3]}****{phone[-4:]}"
