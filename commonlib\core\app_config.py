"""
简化的配置管理器

替代复杂的DI容器，提供简单直接的配置访问方式
"""

from pathlib import Path
from typing import List, Optional, Union

from commonlib.configs.base_setting import AppSettings
from commonlib.configs.config_loader import ConfigLoader
from commonlib.core.tsif_logging import app_logger
from commonlib.utils.singleton import SingletonMeta


class AppConfig(metaclass=SingletonMeta):
    """简化的应用配置管理器

    使用单例模式确保全局配置一致性，提供简单直接的配置访问方式
    """

    def __init__(self):
        self.settings: AppSettings | None = None
        self._config_loader = ConfigLoader()

    def initialize(
        self,
        app_setting: Optional[AppSettings] = None,
        config_path: Optional[Union[str, Path]] = None,
        additional_paths: Optional[List[Union[str, Path]]] = None,
    ) -> "AppConfig":
        """初始化配置

        Args:
            app_setting: 应用设置对象，如果为None则创建默认设置
            config_path: 配置文件路径
            additional_paths: 额外的配置文件搜索路径

        Returns:
            AppConfig: 配置管理器实例
        """
        if not app_setting:
            app_setting = AppSettings()

        self.settings = app_setting

        # 加载配置文件
        self._config_loader.load(app_setting, config_path, additional_paths)

        # 初始化日志
        app_logger.initialize(log_dir=self.log_dir, app_name=self.app_name, debug=self.debug)
        app_logger.info("Configuration and logging initialized successfully")

        return self

    @property
    def app_name(self) -> str:
        """应用名称"""
        return self.settings.application.project_name if self.settings else "unknown"

    @property
    def debug(self) -> bool:
        """调试模式"""
        return self.settings.debug if self.settings else False

    @property
    def log_dir(self) -> str:
        """日志目录"""
        return self.settings.log_dir if self.settings else "./logs"

    @property
    def redis_config(self):
        """Redis配置"""
        return self.settings.persistence.redis if self.settings else None

    @property
    def mysql_config(self):
        """MySQL配置"""
        return self.settings.persistence.mysql if self.settings else None

    @property
    def postgres_config(self):
        """PostgreSQL配置"""
        return self.settings.persistence.postgres if self.settings else None

    @property
    def mongodb_config(self):
        """MongoDB配置"""
        return self.settings.persistence.mongodb if self.settings else None

    @property
    def rabbitmq_config(self):
        """RabbitMQ配置"""
        return self.settings.persistence.rabbitmq if self.settings else None

    @property
    def persistence_config(self):
        """持久化配置"""
        return self.settings.persistence if self.settings else None

    @property
    def connection_priority_config(self):
        """连接优先级配置"""
        return self.settings.connection_priority if self.settings else None

    @property
    def application_config(self):
        """应用配置"""
        return self.settings.application if self.settings else None

    @property
    def message_broker_config(self):
        """消息代理配置"""
        return self.settings.persistence.message_broker if self.settings else None

    @property
    def worker_config(self):
        """Worker配置"""
        return self.settings.persistence.worker if self.settings else None

    def get_config_value(self, key: str, default=None):
        """获取配置值

        Args:
            key: 配置键，支持点号分隔的嵌套访问，如 'persistence.redis.host'
            default: 默认值

        Returns:
            配置值或默认值
        """
        if not self.settings:
            return default

        try:
            value = self.settings
            for part in key.split("."):
                value = getattr(value, part)
            return value
        except (AttributeError, TypeError):
            return default

    def is_initialized(self) -> bool:
        """检查是否已初始化"""
        return self.settings is not None

    @property
    def config(self):
        return self.settings


app_local_config = AppConfig()


def setup_app_config(
    app_setting: Optional[AppConfig] = None,
    config_path: Optional[Union[str, Path]] = None,
    additional_paths: Optional[List[Union[str, Path]]] = None,
) -> AppConfig:
    """设置应用配置

    这是初始化配置的便捷函数，等同于 AppConfig().initialize()

    Args:
        app_setting: 应用设置对象
        config_path: 配置文件路径
        additional_paths: 额外的配置文件搜索路径

    Returns:
        AppConfig: 初始化后的配置管理器实例
    """
    return app_local_config.initialize(app_setting, config_path, additional_paths)


if __name__ == "__main__":
    # 测试配置管理器
    config = setup_app_config()

    print(f"App name: {config.app_name}")
    print(f"Debug mode: {config.debug}")
    print(f"Log directory: {config.log_dir}")
    print(f"Redis config: {config.redis_config}")
    print(f"MySQL config: {config.mysql_config}")

    # 测试配置值获取
    print(f"Redis host: {config.get_config_value('persistence.redis.host', 'localhost')}")

    app_logger.info("Configuration test completed successfully!")
