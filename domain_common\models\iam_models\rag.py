"""
IAM RAG 特定模型

包含与 RAG（检索增强生成）相关的权限和访问控制模型
"""

from typing import Optional

from sqlalchemy import Index, String, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column

from domain_common.models.base_model import Base
from domain_common.models.constants import JSONType
from domain_common.models.fields import Fields
from domain_common.models.mixins import AuditManager, AuditMixin, SoftDeleteMixin, StatusManager


class KnowledgeBase(Base, AuditMixin, SoftDeleteMixin):
    """知识库模型

    管理 RAG 系统中的知识库，支持多租户和访问控制
    """

    __tablename__ = "knowledge_bases"

    # 主键
    kb_id: Mapped[str] = Fields.uuid_primary_key(doc="知识库ID")

    # 多租户字段
    tenant_id: Mapped[str] = Fields.tenant()

    # 基本信息字段
    kb_name: Mapped[str] = Fields.name(doc="知识库名称")
    kb_code: Mapped[str] = Fields.code(doc="知识库编码")
    description: Mapped[Optional[str]] = Fields.long_text(doc="知识库描述")

    # 配置字段
    config: Mapped[Optional[JSONType]] = Fields.json_field(doc="知识库配置")
    embedding_model: Mapped[Optional[str]] = mapped_column(
        String(100), nullable=True, doc="嵌入模型"
    )
    vector_store: Mapped[Optional[str]] = mapped_column(
        String(100), nullable=True, doc="向量存储类型"
    )

    # 状态字段
    status: Mapped[str] = Fields.status()

    # 元数据字段
    meta_data: Mapped[Optional[JSONType]] = Fields.json_field(default={}, doc="知识库元数据")

    __table_args__ = (
        UniqueConstraint("tenant_id", "kb_code", name="uq_knowledge_bases_tenant_code"),
        Index("idx_knowledge_bases_tenant_status", "tenant_id", "status"),
    )

    @property
    def status_manager(self) -> StatusManager:
        """获取状态管理器"""
        return StatusManager(self)

    @property
    def audit_manager(self) -> AuditManager:
        """获取审计管理器"""
        return AuditManager(self)

    def __repr__(self) -> str:
        return (f"<KnowledgeBase(kb_id={self.kb_id}, "
                f"kb_code={self.kb_code}, tenant_id={self.tenant_id})>")


class Document(Base, AuditMixin, SoftDeleteMixin):
    """文档模型

    管理 RAG 系统中的文档，支持多租户和访问控制
    """

    __tablename__ = "documents"

    # 主键
    doc_id: Mapped[str] = Fields.uuid_primary_key(doc="文档ID")

    # 多租户字段
    tenant_id: Mapped[str] = Fields.tenant()

    # 知识库关联
    kb_id: Mapped[str] = mapped_column(String(64), nullable=False, doc="知识库ID")

    # 基本信息字段
    title: Mapped[str] = Fields.name(doc="文档标题")
    doc_type: Mapped[str] = Fields.code(max_length=50, doc="文档类型")
    source_url: Mapped[Optional[str]] = mapped_column(String(500), nullable=True, doc="源文档URL")

    # 内容字段 - 实际内容应存储在向量数据库中
    content_hash: Mapped[Optional[str]] = mapped_column(String(64), nullable=True, doc="内容哈希值")
    chunk_count: Mapped[int] = mapped_column(default=0, doc="分块数量")

    # 状态字段
    status: Mapped[str] = Fields.status()

    # 元数据字段
    meta_data: Mapped[Optional[JSONType]] = Fields.json_field(default={}, doc="文档元数据")

    __table_args__ = (
        Index("idx_documents_tenant_kb", "tenant_id", "kb_id"),
        Index("idx_documents_tenant_status", "tenant_id", "status"),
        Index("idx_documents_doc_type", "doc_type"),
    )

    @property
    def status_manager(self) -> StatusManager:
        """获取状态管理器"""
        return StatusManager(self)

    @property
    def audit_manager(self) -> AuditManager:
        """获取审计管理器"""
        return AuditManager(self)

    def __repr__(self) -> str:
        return f"<Document(doc_id={self.doc_id}, title={self.title}, kb_id={self.kb_id})>"


class KnowledgeBaseAccess(Base):
    """知识库访问权限模型

    管理用户或角色对知识库的访问权限
    """

    __tablename__ = "knowledge_base_access"

    # 主键
    id: Mapped[int] = Fields.bigint_id(doc="访问权限ID")

    # 多租户字段
    tenant_id: Mapped[str] = Fields.tenant()

    # 知识库关联
    kb_id: Mapped[str] = mapped_column(String(64), nullable=False, doc="知识库ID")

    # 主体（用户或角色）
    principal_type: Mapped[str] = Fields.code(max_length=20, doc="主体类型: user, role")
    principal_id: Mapped[str] = mapped_column(String(64), nullable=False, doc="主体ID")

    # 权限级别
    access_level: Mapped[str] = Fields.code(max_length=20, doc="访问级别: read, write, admin")

    # 状态字段
    status: Mapped[str] = Fields.status()

    # 分配信息字段
    assigned_at: Mapped[str] = Fields.created_at()
    assigned_by: Mapped[Optional[str]] = Fields.user(required=False)

    __table_args__ = (
        UniqueConstraint(
            "tenant_id",
            "kb_id",
            "principal_type",
            "principal_id",
            name="uq_kb_access_tenant_kb_principal",
        ),
        Index("idx_kb_access_tenant_kb", "tenant_id", "kb_id"),
        Index("idx_kb_access_principal", "principal_type", "principal_id"),
        Index("idx_kb_access_level", "access_level"),
    )

    def __repr__(self) -> str:
        return (f"<KnowledgeBaseAccess(id={self.id}, "
                f"kb_id={self.kb_id}, principal_id={self.principal_id})>")
