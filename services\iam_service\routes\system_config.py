"""
系统配置路由

提供系统配置管理的API接口，支持租户级和全局配置
"""

from typing import Any, Dict, List, Optional, Union

from container import ServiceContainer
from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends
from pydantic import BaseModel, Field

from commonlib.exceptions.exceptions import ValidationError
from commonlib.schemas.request import BaseRequest
from commonlib.schemas.responses import SuccessResponse, success_response
from services.system_config_service import SystemConfigService

router = APIRouter(prefix="/system", tags=["系统配置"])


# ===== 请求数据模型 =====


class SetConfigRequestData(BaseModel):
    """
    设置配置请求数据模型

    设置系统或租户级配置项
    """

    tenant_id: Optional[str] = Field(
        None,
        description="租户ID（为空时设置全局配置）",
        examples=["550e8400-e29b-41d4-a716-446655440000"],
    )
    config_key: str = Field(..., description="配置键", examples=["password_policy.min_length"])
    config_value: Union[str, int, float, bool, Dict[str, Any], List[Any]] = Field(
        ..., description="配置值", examples=[8]
    )
    description: Optional[str] = Field(None, description="配置描述", examples=["密码最小长度要求"])
    is_sensitive: bool = Field(
        False, description="是否为敏感配置（敏感配置会加密存储）", examples=[False]
    )
    category: str = Field(
        "general",
        description="配置分类：general(通用)、security(安全)、notification(通知)、integration(集成)",
        examples=["security"],
    )


class GetConfigRequestData(BaseModel):
    """
    获取配置请求数据模型

    获取指定配置项的值
    """

    tenant_id: Optional[str] = Field(
        None,
        description="租户ID（为空时获取全局配置）",
        examples=["550e8400-e29b-41d4-a716-446655440000"],
    )
    config_key: str = Field(..., description="配置键", examples=["password_policy.min_length"])
    include_inherited: bool = Field(True, description="是否包含继承的全局配置", examples=[True])


class QueryConfigsRequestData(BaseModel):
    """
    查询配置列表请求数据模型

    查询配置项列表，支持分类和关键词过滤
    """

    tenant_id: Optional[str] = Field(
        None,
        description="租户ID（为空时查询全局配置）",
        examples=["550e8400-e29b-41d4-a716-446655440000"],
    )
    category: Optional[str] = Field(None, description="配置分类", examples=["security"])
    keyword: Optional[str] = Field(
        None, description="关键词搜索（搜索配置键和描述）", examples=["password"]
    )
    include_inherited: bool = Field(True, description="是否包含继承的全局配置", examples=[True])
    include_sensitive: bool = Field(
        False, description="是否包含敏感配置（敏感配置值会脱敏）", examples=[False]
    )
    page: int = Field(1, description="页码", ge=1, examples=[1])
    page_size: int = Field(50, description="每页数量", ge=1, le=200, examples=[50])


class DeleteConfigRequestData(BaseModel):
    """
    删除配置请求数据模型

    删除指定配置项
    """

    tenant_id: Optional[str] = Field(
        None,
        description="租户ID（为空时删除全局配置）",
        examples=["550e8400-e29b-41d4-a716-446655440000"],
    )
    config_key: str = Field(..., description="配置键", examples=["custom_setting.feature_flag"])


class BatchSetConfigsRequestData(BaseModel):
    """
    批量设置配置请求数据模型

    批量设置多个配置项
    """

    tenant_id: Optional[str] = Field(
        None,
        description="租户ID（为空时设置全局配置）",
        examples=["550e8400-e29b-41d4-a716-446655440000"],
    )
    configs: List[Dict[str, Any]] = Field(
        ...,
        description="配置项列表",
        examples=[
            [
                {
                    "config_key": "password_policy.min_length",
                    "config_value": 8,
                    "description": "密码最小长度",
                    "category": "security",
                },
                {
                    "config_key": "session.timeout_hours",
                    "config_value": 24,
                    "description": "会话超时时间（小时）",
                    "category": "security",
                },
            ]
        ],
    )


class ResetConfigsRequestData(BaseModel):
    """
    重置配置请求数据模型

    重置配置到默认值
    """

    tenant_id: Optional[str] = Field(
        None,
        description="租户ID（为空时重置全局配置）",
        examples=["550e8400-e29b-41d4-a716-446655440000"],
    )
    category: Optional[str] = Field(
        None, description="配置分类（为空时重置所有配置）", examples=["security"]
    )
    confirm: bool = Field(False, description="确认重置（必须为true）", examples=[True])


# ===== 响应数据模型 =====


class ConfigInfo(BaseModel):
    """配置信息模型"""

    config_id: str = Field(
        ...,
        description="配置ID",
        examples=["config_550e8400-e29b-41d4-a716-446655440000"],
    )
    tenant_id: Optional[str] = Field(None, description="租户ID")
    config_key: str = Field(..., description="配置键", examples=["password_policy.min_length"])
    config_value: Union[str, int, float, bool, Dict[str, Any], List[Any]] = Field(
        ..., description="配置值"
    )
    description: Optional[str] = Field(None, description="配置描述")
    is_sensitive: bool = Field(..., description="是否为敏感配置", examples=[False])
    category: str = Field(..., description="配置分类", examples=["security"])
    is_inherited: bool = Field(..., description="是否为继承配置", examples=[False])
    created_at: str = Field(..., description="创建时间")
    updated_at: Optional[str] = Field(None, description="更新时间")


class SetConfigResponse(BaseModel):
    """设置配置响应数据"""

    config_id: str = Field(..., description="配置ID")
    config_key: str = Field(..., description="配置键")
    previous_value: Optional[Union[str, int, float, bool, Dict[str, Any], List[Any]]] = Field(
        None, description="之前的值"
    )
    new_value: Union[str, int, float, bool, Dict[str, Any], List[Any]] = Field(
        ..., description="新值"
    )
    updated_at: str = Field(..., description="更新时间")


class GetConfigResponse(BaseModel):
    """获取配置响应数据"""

    config_key: str = Field(..., description="配置键")
    config_value: Union[str, int, float, bool, Dict[str, Any], List[Any]] = Field(
        ..., description="配置值"
    )
    is_inherited: bool = Field(..., description="是否为继承配置")
    source: str = Field(..., description="配置来源：tenant(租户)、global(全局)、default(默认)")


class QueryConfigsResponse(BaseModel):
    """查询配置列表响应数据"""

    configs: List[ConfigInfo] = Field(..., description="配置列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    has_next: bool = Field(..., description="是否有下一页")


class BatchSetConfigsResponse(BaseModel):
    """批量设置配置响应数据"""

    success_count: int = Field(..., description="成功设置的配置数量")
    failed_count: int = Field(..., description="失败的配置数量")
    failed_configs: List[Dict[str, str]] = Field(..., description="失败的配置列表")
    updated_at: str = Field(..., description="更新时间")


class ResetConfigsResponse(BaseModel):
    """重置配置响应数据"""

    reset_count: int = Field(..., description="重置的配置数量")
    category: Optional[str] = Field(None, description="重置的分类")
    reset_at: str = Field(..., description="重置时间")


# ===== 响应模型包装类 =====


class SetConfigResponseModel(SuccessResponse[SetConfigResponse]):
    """设置配置响应模型"""

    data: SetConfigResponse = Field(..., description="设置配置结果")


class GetConfigResponseModel(SuccessResponse[GetConfigResponse]):
    """获取配置响应模型"""

    data: GetConfigResponse = Field(..., description="配置信息")


class QueryConfigsResponseModel(SuccessResponse[QueryConfigsResponse]):
    """查询配置列表响应模型"""

    data: QueryConfigsResponse = Field(..., description="配置列表数据")


class BatchSetConfigsResponseModel(SuccessResponse[BatchSetConfigsResponse]):
    """批量设置配置响应模型"""

    data: BatchSetConfigsResponse = Field(..., description="批量设置结果")


class ResetConfigsResponseModel(SuccessResponse[ResetConfigsResponse]):
    """重置配置响应模型"""

    data: ResetConfigsResponse = Field(..., description="重置结果")


# ===== 路由端点 =====


@router.post(
    "/config/set",
    summary="设置配置",
    description="""
    设置系统配置

    **功能说明：**
    - 设置系统或租户级配置项
    - 支持多种数据类型的配置值
    - 支持敏感配置加密存储
    - 支持配置分类管理

    **配置层级：**
    - 全局配置：影响所有租户
    - 租户配置：仅影响指定租户
    - 租户配置优先级高于全局配置

    **返回数据：**
    - 配置ID和键名
    - 之前的值和新值
    - 更新时间
    """,
    response_model=SetConfigResponseModel,
    responses={
        200: {"description": "配置设置成功"},
        400: {"description": "请求参数错误"},
        500: {"description": "服务器内部错误"},
    },
    tags=["配置管理"],
)
@inject
async def set_config(
    request: BaseRequest[SetConfigRequestData],
    config_service: SystemConfigService = Depends(Provide[ServiceContainer.system_config_service]),
):
    """
    设置配置

    设置系统或租户级配置项
    """
    config_params = request.data
    result = await config_service.set_config(
        tenant_id=config_params.tenant_id,
        config_key=config_params.config_key,
        config_value=config_params.config_value,
        description=config_params.description,
        is_sensitive=config_params.is_sensitive,
        category=config_params.category,
    )
    return success_response(result, message="配置设置成功")


@router.post(
    "/config/get",
    summary="获取配置",
    description="""
    获取配置值

    **功能说明：**
    - 获取指定配置项的值
    - 支持配置继承（租户配置 > 全局配置 > 默认值）
    - 自动处理敏感配置脱敏
    - 返回配置来源信息

    **配置查找顺序：**
    1. 租户级配置
    2. 全局配置
    3. 系统默认值

    **返回数据：**
    - 配置键和值
    - 是否为继承配置
    - 配置来源
    """,
    response_model=GetConfigResponseModel,
    responses={
        200: {"description": "获取成功"},
        404: {"description": "配置不存在"},
        500: {"description": "服务器内部错误"},
    },
    tags=["配置管理"],
)
@inject
async def get_config(
    request: BaseRequest[GetConfigRequestData],
    config_service: SystemConfigService = Depends(Provide[ServiceContainer.system_config_service]),
):
    """
    获取配置

    获取指定配置项的值
    """
    config_params = request.data
    result = await config_service.get_config(
        tenant_id=config_params.tenant_id,
        config_key=config_params.config_key,
        include_inherited=config_params.include_inherited,
    )
    return success_response(result, message="获取成功")


@router.post(
    "/config/query",
    summary="查询配置列表",
    description="""
    查询配置列表

    **功能说明：**
    - 查询配置项列表
    - 支持分类和关键词过滤
    - 支持分页查询
    - 支持敏感配置脱敏显示

    **查询条件：**
    - 配置分类过滤
    - 关键词搜索
    - 是否包含继承配置
    - 是否包含敏感配置

    **返回数据：**
    - 配置列表
    - 分页信息
    - 配置详细信息
    """,
    response_model=QueryConfigsResponseModel,
    responses={
        200: {"description": "查询成功"},
        400: {"description": "请求参数错误"},
        500: {"description": "服务器内部错误"},
    },
    tags=["配置管理"],
)
@inject
async def query_configs(
    request: BaseRequest[QueryConfigsRequestData],
    config_service: SystemConfigService = Depends(Provide[ServiceContainer.system_config_service]),
):
    """
    查询配置列表

    支持多条件查询和分页的配置列表查询
    """
    query_params = request.data
    result = await config_service.query_configs(
        tenant_id=query_params.tenant_id,
        category=query_params.category,
        keyword=query_params.keyword,
        include_inherited=query_params.include_inherited,
        include_sensitive=query_params.include_sensitive,
        page=query_params.page,
        page_size=query_params.page_size,
    )
    return success_response(result, message="查询成功")


@router.post(
    "/config/delete",
    summary="删除配置",
    description="""
    删除配置

    **功能说明：**
    - 删除指定配置项
    - 支持全局和租户级配置删除
    - 删除后恢复到上级配置或默认值
    - 记录删除操作审计日志

    **注意事项：**
    - 删除租户配置后会使用全局配置
    - 删除全局配置后会使用系统默认值
    - 系统核心配置不可删除

    **返回数据：**
    - 删除结果确认
    - 删除时间
    """,
    response_model=SuccessResponse[Dict[str, Any]],
    responses={
        200: {"description": "删除成功"},
        400: {"description": "请求参数错误"},
        403: {"description": "系统配置不可删除"},
        404: {"description": "配置不存在"},
        500: {"description": "服务器内部错误"},
    },
    tags=["配置管理"],
)
@inject
async def delete_config(
    request: BaseRequest[DeleteConfigRequestData],
    config_service: SystemConfigService = Depends(Provide[ServiceContainer.system_config_service]),
):
    """
    删除配置

    删除指定配置项
    """
    delete_params = request.data
    result = await config_service.delete_config(
        tenant_id=delete_params.tenant_id, config_key=delete_params.config_key
    )
    return success_response(result, message="配置删除成功")


@router.post(
    "/config/batch-set",
    summary="批量设置配置",
    description="""
    批量设置配置

    **功能说明：**
    - 批量设置多个配置项
    - 支持事务性操作（全部成功或全部失败）
    - 支持部分成功模式
    - 返回详细的操作结果

    **操作模式：**
    - 事务模式：任一配置失败则全部回滚
    - 部分成功模式：记录失败项，继续处理其他项

    **返回数据：**
    - 成功和失败的配置数量
    - 失败配置的详细信息
    - 操作时间
    """,
    response_model=BatchSetConfigsResponseModel,
    responses={
        200: {"description": "批量设置完成"},
        400: {"description": "请求参数错误"},
        500: {"description": "服务器内部错误"},
    },
    tags=["配置管理"],
)
@inject
async def batch_set_configs(
    request: BaseRequest[BatchSetConfigsRequestData],
    config_service: SystemConfigService = Depends(Provide[ServiceContainer.system_config_service]),
):
    """
    批量设置配置

    批量设置多个配置项
    """
    batch_params = request.data
    result = await config_service.batch_set_configs(
        tenant_id=batch_params.tenant_id, configs=batch_params.configs
    )
    return success_response(result, message="批量设置完成")


@router.post(
    "/config/reset",
    summary="重置配置",
    description="""
    重置配置

    **功能说明：**
    - 重置配置到默认值
    - 支持按分类重置
    - 支持全部重置
    - 需要确认操作

    **重置范围：**
    - 指定分类：重置该分类下的所有配置
    - 全部重置：重置所有自定义配置
    - 保留系统核心配置

    **返回数据：**
    - 重置的配置数量
    - 重置的分类
    - 重置时间
    """,
    response_model=ResetConfigsResponseModel,
    responses={
        200: {"description": "重置成功"},
        400: {"description": "请求参数错误或未确认"},
        500: {"description": "服务器内部错误"},
    },
    tags=["配置管理"],
)
@inject
async def reset_configs(
    request: BaseRequest[ResetConfigsRequestData],
    config_service: SystemConfigService = Depends(Provide[ServiceContainer.system_config_service]),
):
    """
    重置配置

    重置配置到默认值
    """
    reset_params = request.data

    if not reset_params.confirm:
        raise ValidationError("必须确认重置操作")

    result = await config_service.reset_configs(
        tenant_id=reset_params.tenant_id, category=reset_params.category
    )
    return success_response(result, message="配置重置成功")
