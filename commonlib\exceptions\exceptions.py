from typing import Any, Dict, List, Optional

from fastapi import HTTPException

from commonlib.exceptions.error_codes import (
    AuthErrors,
    BusinessErrors,
    ConflictErrors,
    DatabaseErrors,
    ErrorCode,
    ExternalServiceErrors,
    IntegrationErrors,
    NotFoundErrors,
    PermissionErrors,
    RAGErrors,
    RateLimitErrors,
    SystemErrors,
    ValidationErrors,
)


class APIError(HTTPException):
    """标准化异常适配层"""

    def __init__(
        self,
        error_code: ErrorCode,
        detail: Optional[str] = None,
        headers: Optional[Dict[str, str]] = None,
        **ctx: Any,
    ):
        # 构造错误响应体
        error_body = {
            "error": {
                "type": error_code.type,
                "message": detail or error_code.meta.i18n_key or "",
                **({"details": ctx} if ctx else {}),
            }
        }

        # 添加监控头信息
        extended_headers = {
            "X-Error-Code": str(error_code.code),
            "X-Error-Level": error_code.meta.level.value,
            **(headers or {}),
        }

        super().__init__(
            status_code=error_code.meta.http_status,
            detail=error_body,
            headers=extended_headers,
        )

        # 保留原始信息
        self.error_code = error_code
        self.context = ctx


# ===== 具体异常快捷方式 =====
class BadRequestError(APIError):
    """400错误快捷方式"""

    def __init__(self, message: str, **ctx):
        super().__init__(error_code=ValidationErrors.INVALID_FORMAT, detail=message, **ctx)


class ValidationError(APIError):
    """数据验证错误"""

    def __init__(
        self,
        message: str,
        field_name: Optional[str] = None,
        field_value: Optional[Any] = None,
        validation_type: str = "format",
    ):
        # 根据验证类型选择合适的错误代码
        error_code_map = {
            "format": ValidationErrors.INVALID_FORMAT,
            "missing": ValidationErrors.MISSING_FIELD,
            "value": ValidationErrors.INVALID_VALUE,
            "length": ValidationErrors.FIELD_TOO_LONG,
            "email": ValidationErrors.INVALID_EMAIL,
            "phone": ValidationErrors.INVALID_PHONE,
            "date": ValidationErrors.INVALID_DATE,
            "json": ValidationErrors.INVALID_JSON,
            "schema": ValidationErrors.SCHEMA_VALIDATION_FAILED,
        }

        error_code = error_code_map.get(validation_type, ValidationErrors.INVALID_FORMAT)

        super().__init__(
            error_code=error_code,
            detail=message,
            field_name=field_name,
            field_value=field_value,
            validation_type=validation_type,
        )


# ===== 详细的验证异常类型 =====
class MissingFieldError(APIError):
    """缺少字段异常"""

    def __init__(self, field_name: str, message: Optional[str] = None):
        if not message:
            message = f"缺少必填字段: {field_name}"
        super().__init__(
            error_code=ValidationErrors.MISSING_FIELD,
            detail=message,
            field_name=field_name,
        )


class InvalidValueError(APIError):
    """无效值异常"""

    def __init__(self, field_name: str, field_value: Any, message: Optional[str] = None):
        if not message:
            message = f"字段 {field_name} 的值无效: {field_value}"
        super().__init__(
            error_code=ValidationErrors.INVALID_VALUE,
            detail=message,
            field_name=field_name,
            field_value=field_value,
        )


class FieldTooLongError(APIError):
    """字段过长异常"""

    def __init__(
        self,
        field_name: str,
        current_length: int,
        max_length: int,
        message: Optional[str] = None,
    ):
        if not message:
            message = f"字段 {field_name} 长度超限: {current_length} > {max_length}"
        super().__init__(
            error_code=ValidationErrors.FIELD_TOO_LONG,
            detail=message,
            field_name=field_name,
            current_length=current_length,
            max_length=max_length,
        )


class FieldTooShortError(APIError):
    """字段过短异常"""

    def __init__(
        self,
        field_name: str,
        current_length: int,
        min_length: int,
        message: Optional[str] = None,
    ):
        if not message:
            message = f"字段 {field_name} 长度不足: {current_length} < {min_length}"
        super().__init__(
            error_code=ValidationErrors.FIELD_TOO_SHORT,
            detail=message,
            field_name=field_name,
            current_length=current_length,
            min_length=min_length,
        )


class InvalidEmailError(APIError):
    """邮箱格式无效异常"""

    def __init__(self, email: str, message: Optional[str] = None):
        if not message:
            message = f"邮箱格式不正确: {email}"
        super().__init__(error_code=ValidationErrors.INVALID_EMAIL, detail=message, email=email)


class InvalidPhoneError(APIError):
    """手机号格式无效异常"""

    def __init__(self, phone: str, message: Optional[str] = None):
        if not message:
            message = f"手机号格式不正确: {phone}"
        super().__init__(error_code=ValidationErrors.INVALID_PHONE, detail=message, phone=phone)


class InvalidDateError(APIError):
    """日期格式无效异常"""

    def __init__(
        self,
        date_value: str,
        expected_format: Optional[str] = None,
        message: Optional[str] = None,
    ):
        if not message:
            message = f"日期格式不正确: {date_value}"
            if expected_format:
                message += f" (期望格式: {expected_format})"
        super().__init__(
            error_code=ValidationErrors.INVALID_DATE,
            detail=message,
            date_value=date_value,
            expected_format=expected_format,
        )


class InvalidJsonError(APIError):
    """JSON格式无效异常"""

    def __init__(
        self,
        json_data: str,
        parse_error: Optional[str] = None,
        message: Optional[str] = None,
    ):
        if not message:
            message = "JSON格式不正确"
            if parse_error:
                message += f": {parse_error}"
        super().__init__(
            error_code=ValidationErrors.INVALID_JSON,
            detail=message,
            json_data=json_data[:100] + "..." if len(json_data) > 100 else json_data,
            parse_error=parse_error,
        )


class SchemaValidationError(APIError):
    """模式验证失败异常"""

    def __init__(self, schema_errors: List[str], message: Optional[str] = None):
        if not message:
            message = f"模式验证失败: {len(schema_errors)} 个错误"
        super().__init__(
            error_code=ValidationErrors.SCHEMA_VALIDATION_FAILED,
            detail=message,
            schema_errors=schema_errors,
            error_count=len(schema_errors),
        )


class UnsupportedFileTypeError(APIError):
    """不支持的文件类型异常"""

    def __init__(
        self,
        file_type: str,
        supported_types: Optional[List[str]] = None,
        message: Optional[str] = None,
    ):
        if not message:
            message = f"不支持的文件类型: {file_type}"
            if supported_types:
                message += f" (支持的类型: {', '.join(supported_types)})"
        super().__init__(
            error_code=RAGErrors.UNSUPPORTED_FILE_TYPE,
            detail=message,
            file_type=file_type,
            supported_types=supported_types,
        )


class FileTooLargeError(APIError):
    """文件过大异常"""

    def __init__(
        self,
        file_size: int,
        max_size: int,
        file_name: Optional[str] = None,
        message: Optional[str] = None,
    ):
        if not message:
            message = f"文件大小超限: {file_size} > {max_size} bytes"
            if file_name:
                message = f"文件 {file_name} 大小超限: {file_size} > {max_size} bytes"
        super().__init__(
            error_code=RAGErrors.FILE_TOO_LARGE,
            detail=message,
            file_size=file_size,
            max_size=max_size,
            file_name=file_name,
        )


class UnauthorizedError(APIError):
    """401错误快捷方式"""

    def __init__(self, message: str = "需要认证"):
        super().__init__(error_code=AuthErrors.INVALID_CREDENTIALS, detail=message)


# ===== 详细的认证异常类型 =====
class ExpiredTokenError(APIError):
    """令牌过期异常"""

    def __init__(self, token_type: str = "access_token", message: Optional[str] = None):
        if not message:
            message = f"{token_type} 已过期"
        super().__init__(error_code=AuthErrors.EXPIRED_TOKEN, detail=message, token_type=token_type)


class InvalidTokenError(APIError):
    """无效令牌异常"""

    def __init__(
        self,
        token_type: str = "access_token",
        reason: Optional[str] = None,
        message: Optional[str] = None,
    ):
        if not message:
            message = f"{token_type} 无效"
            if reason:
                message += f": {reason}"
        super().__init__(
            error_code=AuthErrors.INVALID_TOKEN,
            detail=message,
            token_type=token_type,
            reason=reason,
        )


class MissingTokenError(APIError):
    """缺少令牌异常"""

    def __init__(self, token_type: str = "access_token", message: Optional[str] = None):
        if not message:
            message = f"缺少 {token_type}"
        super().__init__(error_code=AuthErrors.MISSING_TOKEN, detail=message, token_type=token_type)


class TokenRevokedError(APIError):
    """令牌已撤销异常"""

    def __init__(
        self,
        token_type: str = "access_token",
        revoked_at: Optional[str] = None,
        message: Optional[str] = None,
    ):
        if not message:
            message = f"{token_type} 已被撤销"
            if revoked_at:
                message += f" (撤销时间: {revoked_at})"
        super().__init__(
            error_code=AuthErrors.TOKEN_REVOKED,
            detail=message,
            token_type=token_type,
            revoked_at=revoked_at,
        )


class InvalidRefreshTokenError(APIError):
    """无效刷新令牌异常"""

    def __init__(self, reason: Optional[str] = None, message: Optional[str] = None):
        if not message:
            message = "刷新令牌无效"
            if reason:
                message += f": {reason}"
        super().__init__(error_code=AuthErrors.INVALID_REFRESH_TOKEN, detail=message, reason=reason)


class AccountLockedError(APIError):
    """账户锁定异常"""

    def __init__(
        self,
        user_id: Optional[str] = None,
        locked_until: Optional[str] = None,
        message: Optional[str] = None,
    ):
        if not message:
            message = "账户已被锁定"
            if locked_until:
                message += f" (解锁时间: {locked_until})"
        super().__init__(
            error_code=AuthErrors.ACCOUNT_LOCKED,
            detail=message,
            user_id=user_id,
            locked_until=locked_until,
        )


class AccountDisabledError(APIError):
    """账户禁用异常"""

    def __init__(
        self,
        user_id: Optional[str] = None,
        reason: Optional[str] = None,
        message: Optional[str] = None,
    ):
        if not message:
            message = "账户已被禁用"
            if reason:
                message += f": {reason}"
        super().__init__(
            error_code=AuthErrors.ACCOUNT_DISABLED,
            detail=message,
            user_id=user_id,
            reason=reason,
        )


# ===== 详细的业务逻辑异常类型 =====
class BusinessError(APIError):
    """业务操作异常"""

    def __init__(
        self,
        operation: str,
        reason: Optional[str] = None,
        message: Optional[str] = None,
    ):
        if not message:
            message = f"无效操作: {operation}"
            if reason:
                message += f" (原因: {reason})"
        super().__init__(
            error_code=BusinessErrors.INVALID_OPERATION,
            detail=message,
            operation=operation,
            reason=reason,
        )


class InvalidOperationError(APIError):
    """无效操作异常"""

    def __init__(
        self,
        operation: str,
        reason: Optional[str] = None,
        message: Optional[str] = None,
    ):
        if not message:
            message = f"无效操作: {operation}"
            if reason:
                message += f" (原因: {reason})"
        super().__init__(
            error_code=BusinessErrors.INVALID_OPERATION,
            detail=message,
            operation=operation,
            reason=reason,
        )


class InvalidStateError(APIError):
    """无效状态异常"""

    def __init__(
        self,
        current_state: str,
        expected_state: Optional[str] = None,
        message: Optional[str] = None,
    ):
        if not message:
            message = f"状态无效: {current_state}"
            if expected_state:
                message += f" (期望状态: {expected_state})"
        super().__init__(
            error_code=BusinessErrors.INVALID_STATE,
            detail=message,
            current_state=current_state,
            expected_state=expected_state,
        )


class OperationFailedError(APIError):
    """操作失败异常"""

    def __init__(
        self,
        operation: str,
        error_details: Optional[str] = None,
        message: Optional[str] = None,
    ):
        if not message:
            message = f"操作失败: {operation}"
            if error_details:
                message += f" ({error_details})"
        super().__init__(
            error_code=BusinessErrors.OPERATION_FAILED,
            detail=message,
            operation=operation,
            error_details=error_details,
        )


class ResourceLockedError(APIError):
    """资源锁定异常"""

    def __init__(
        self,
        resource_type: str,
        resource_id: str,
        locked_by: Optional[str] = None,
        message: Optional[str] = None,
    ):
        if not message:
            message = f"资源已被锁定: {resource_type} (ID: {resource_id})"
            if locked_by:
                message += f" (锁定者: {locked_by})"
        super().__init__(
            error_code=BusinessErrors.RESOURCE_LOCKED,
            detail=message,
            resource_type=resource_type,
            resource_id=resource_id,
            locked_by=locked_by,
        )


class QuotaExceededError(APIError):
    """配额超限异常"""

    def __init__(
        self,
        quota_type: str,
        current_usage: int,
        quota_limit: int,
        message: Optional[str] = None,
    ):
        if not message:
            message = f"{quota_type} 配额超限: {current_usage}/{quota_limit}"
        super().__init__(
            error_code=BusinessErrors.QUOTA_EXCEEDED,
            detail=message,
            quota_type=quota_type,
            current_usage=current_usage,
            quota_limit=quota_limit,
        )


class NotFoundError(APIError):
    """404错误快捷方式"""

    def __init__(self, resource: str, resource_id: Any = None):
        message = f"{resource}不存在" + (f" (ID: {resource_id})" if resource_id else "")
        super().__init__(
            error_code=NotFoundErrors.RESOURCE_NOT_FOUND,
            detail=message,
            resource=resource,
            resource_id=resource_id,
        )


# ===== 详细的NotFound异常类型 =====
class UserNotFoundError(APIError):
    """用户不存在异常"""

    def __init__(self, user_id: str, message: Optional[str] = None):
        if not message:
            message = f"用户不存在: {user_id}"
        super().__init__(error_code=NotFoundErrors.USER_NOT_FOUND, detail=message, user_id=user_id)


class EndpointNotFoundError(APIError):
    """端点不存在异常"""

    def __init__(self, endpoint: str, method: Optional[str] = None, message: Optional[str] = None):
        if not message:
            message = f"端点不存在: {endpoint}"
            if method:
                message += f" (方法: {method})"
        super().__init__(
            error_code=NotFoundErrors.ENDPOINT_NOT_FOUND,
            detail=message,
            endpoint=endpoint,
            method=method,
        )


class FileNotFoundError(APIError):
    """文件不存在异常"""

    def __init__(self, file_path: str, message: Optional[str] = None):
        if not message:
            message = f"文件不存在: {file_path}"
        super().__init__(
            error_code=NotFoundErrors.FILE_NOT_FOUND,
            detail=message,
            file_path=file_path,
        )


class PageNotFoundError(APIError):
    """页面不存在异常"""

    def __init__(self, page_url: str, message: Optional[str] = None):
        if not message:
            message = f"页面不存在: {page_url}"
        super().__init__(
            error_code=NotFoundErrors.PAGE_NOT_FOUND, detail=message, page_url=page_url
        )


class ForbiddenError(APIError):
    """403权限错误快捷方式"""

    def __init__(self, message: str = "权限不足", operation: Optional[str] = None):
        super().__init__(
            error_code=PermissionErrors.INSUFFICIENT_PERMISSION,
            detail=message,
            operation=operation,
        )


# ===== 详细的权限异常类型 =====
class AccessDeniedError(APIError):
    """访问拒绝异常"""

    def __init__(self, resource: str, action: Optional[str] = None, message: Optional[str] = None):
        if not message:
            message = f"拒绝访问资源: {resource}"
            if action:
                message += f" (操作: {action})"
        super().__init__(
            error_code=PermissionErrors.ACCESS_DENIED,
            detail=message,
            resource=resource,
            action=action,
        )


class ResourceForbiddenError(APIError):
    """资源禁止访问异常"""

    def __init__(
        self,
        resource_type: str,
        resource_id: Optional[str] = None,
        message: Optional[str] = None,
    ):
        if not message:
            message = f"禁止访问 {resource_type}"
            if resource_id:
                message += f" (ID: {resource_id})"
        super().__init__(
            error_code=PermissionErrors.RESOURCE_FORBIDDEN,
            detail=message,
            resource_type=resource_type,
            resource_id=resource_id,
        )


class OperationNotAllowedError(APIError):
    """操作不允许异常"""

    def __init__(
        self,
        operation: str,
        reason: Optional[str] = None,
        message: Optional[str] = None,
    ):
        if not message:
            message = f"操作不被允许: {operation}"
            if reason:
                message += f" (原因: {reason})"
        super().__init__(
            error_code=PermissionErrors.OPERATION_NOT_ALLOWED,
            detail=message,
            operation=operation,
            reason=reason,
        )


class RoleRequiredError(APIError):
    """需要特定角色异常"""

    def __init__(
        self,
        required_role: str,
        current_role: Optional[str] = None,
        message: Optional[str] = None,
    ):
        if not message:
            message = f"需要角色: {required_role}"
            if current_role:
                message += f" (当前角色: {current_role})"
        super().__init__(
            error_code=PermissionErrors.ROLE_REQUIRED,
            detail=message,
            required_role=required_role,
            current_role=current_role,
        )


class AuthenticationError(APIError):
    """身份验证失败异常（未提供或无效的认证凭证）

    当用户未登录或提供的认证信息（如Token）无效时抛出。
    对应HTTP 401状态码。

    Attributes:
        required_role: 请求该资源需要的角色（用于提示）
        current_role: 用户当前角色（若有）
        message: 自定义错误消息（若未提供则自动生成）
    """

    def __init__(
        self,
        required_role: str,
        current_role: Optional[str] = None,
        message: Optional[str] = None,
    ):
        if not message:
            message = f"需要角色: {required_role}"
            if current_role:
                message += f" (当前角色: {current_role})"
        super().__init__(
            error_code=PermissionErrors.AUTHENTICATION,
            detail=message,
            required_role=required_role,
            current_role=current_role,
        )


class AuthorizationError(APIError):
    """权限不足异常（认证通过但无权访问）

    当用户已登录但角色/权限不足以访问资源时抛出。
    对应HTTP 403状态码。

    Attributes:
        required_role: 访问该资源必需的角色
        current_role: 用户当前角色（用于对比提示）
        message: 自定义错误消息（若未提供则自动生成）
    """

    def __init__(
        self,
        required_role: str,
        current_role: Optional[str] = None,
        message: Optional[str] = None,
    ):
        if not message:
            message = f"需要角色: {required_role}"
            if current_role:
                message += f" (当前角色: {current_role})"
        super().__init__(
            error_code=PermissionErrors.AUTHORIZATION,
            detail=message,
            required_role=required_role,
            current_role=current_role,
        )


# ===== 详细的限流异常类型 =====
class ApiQuotaExceededError(APIError):
    """API配额超限异常"""

    def __init__(
        self,
        api_name: str,
        quota_limit: int,
        reset_time: Optional[str] = None,
        message: Optional[str] = None,
    ):
        if not message:
            message = f"API {api_name} 配额超限: {quota_limit}"
            if reset_time:
                message += f" (重置时间: {reset_time})"
        super().__init__(
            error_code=RateLimitErrors.API_QUOTA_EXCEEDED,
            detail=message,
            api_name=api_name,
            quota_limit=quota_limit,
            reset_time=reset_time,
        )


class ConcurrentLimitExceededError(APIError):
    """并发限制超限异常"""

    def __init__(
        self,
        current_connections: int,
        max_connections: int,
        message: Optional[str] = None,
    ):
        if not message:
            message = f"并发连接数超限: {current_connections}/{max_connections}"
        super().__init__(
            error_code=RateLimitErrors.CONCURRENT_LIMIT_EXCEEDED,
            detail=message,
            current_connections=current_connections,
            max_connections=max_connections,
        )


# ===== 详细的冲突异常类型 =====
class VersionConflictError(APIError):
    """版本冲突异常"""

    def __init__(
        self,
        resource_type: str,
        resource_id: str,
        expected_version: str,
        actual_version: str,
        message: Optional[str] = None,
    ):
        if not message:
            message = f"{resource_type} 版本冲突: 期望 {expected_version}, 实际 {actual_version}"
        super().__init__(
            error_code=ConflictErrors.VERSION_CONFLICT,
            detail=message,
            resource_type=resource_type,
            resource_id=resource_id,
            expected_version=expected_version,
            actual_version=actual_version,
        )


class DuplicateResourceError(APIError):
    """重复资源异常"""

    def __init__(
        self,
        resource_type: str,
        duplicate_field: str,
        duplicate_value: str,
        message: Optional[str] = None,
    ):
        if not message:
            message = f"{resource_type} 已存在: {duplicate_field} = {duplicate_value}"
        super().__init__(
            error_code=ConflictErrors.DUPLICATE_RESOURCE,
            detail=message,
            resource_type=resource_type,
            duplicate_field=duplicate_field,
            duplicate_value=duplicate_value,
        )


class DatabaseError(APIError):
    """数据库错误快捷方式"""

    def __init__(self, message: str = "数据库操作失败", operation: Optional[str] = None):
        super().__init__(
            error_code=DatabaseErrors.QUERY_FAILED, detail=message, operation=operation
        )


# ===== 详细的数据库异常类型 =====
class DatabaseConnectionError(APIError):
    """数据库连接失败异常"""

    def __init__(
        self,
        database_name: Optional[str] = None,
        connection_string: Optional[str] = None,
        message: Optional[str] = None,
    ):
        if not message:
            message = "数据库连接失败"
            if database_name:
                message += f": {database_name}"
        super().__init__(
            error_code=DatabaseErrors.CONNECTION_FAILED,
            detail=message,
            database_name=database_name,
            connection_string=(
                connection_string[:50] + "..."
                if connection_string and len(connection_string) > 50
                else connection_string
            ),
        )


class DatabaseTransactionError(APIError):
    """数据库事务失败异常"""

    def __init__(
        self,
        transaction_id: Optional[str] = None,
        operation: Optional[str] = None,
        message: Optional[str] = None,
    ):
        if not message:
            message = "数据库事务失败"
            if operation:
                message += f": {operation}"
        super().__init__(
            error_code=DatabaseErrors.TRANSACTION_FAILED,
            detail=message,
            transaction_id=transaction_id,
            operation=operation,
        )


class DatabaseConstraintError(APIError):
    """数据库约束违反异常"""

    def __init__(
        self,
        constraint_name: str,
        table_name: Optional[str] = None,
        message: Optional[str] = None,
    ):
        if not message:
            message = f"违反数据库约束: {constraint_name}"
            if table_name:
                message += f" (表: {table_name})"
        super().__init__(
            error_code=DatabaseErrors.CONSTRAINT_VIOLATION,
            detail=message,
            constraint_name=constraint_name,
            table_name=table_name,
        )


class DatabaseDuplicateKeyError(APIError):
    """数据库重复键异常"""

    def __init__(
        self,
        key_name: str,
        key_value: str,
        table_name: Optional[str] = None,
        message: Optional[str] = None,
    ):
        if not message:
            message = f"重复键冲突: {key_name} = {key_value}"
            if table_name:
                message += f" (表: {table_name})"
        super().__init__(
            error_code=DatabaseErrors.DUPLICATE_KEY,
            detail=message,
            key_name=key_name,
            key_value=key_value,
            table_name=table_name,
        )


class DatabaseTimeoutError(APIError):
    """数据库超时异常"""

    def __init__(
        self,
        operation: str,
        timeout_seconds: Optional[int] = None,
        message: Optional[str] = None,
    ):
        if not message:
            message = f"数据库操作超时: {operation}"
            if timeout_seconds:
                message += f" (超时时间: {timeout_seconds}s)"
        super().__init__(
            error_code=DatabaseErrors.TIMEOUT,
            detail=message,
            operation=operation,
            timeout_seconds=timeout_seconds,
        )


class ConflictException(APIError):
    """409冲突异常 (对应ErrorDomain.CONFLICT)"""

    def __init__(
        self,
        resource_type: str,
        conflict_field: Optional[str] = None,
        existing_id: Optional[Any] = None,
    ):
        """
        :param resource_type: 冲突的资源类型 (如 'user')
        :param conflict_field: 引发冲突的字段 (如 'email')
        :param existing_id: 已存在的资源ID
        """
        message = f"该{resource_type}已存在"
        if conflict_field:
            message += f" (冲突字段: {conflict_field})"

        super().__init__(
            error_code=ConflictErrors.RESOURCE_CONFLICT,
            detail=message,
            resource_type=resource_type,
            conflict_field=conflict_field,
            existing_id=existing_id,
        )


class RateLimitException(APIError):
    """429限流异常"""

    def __init__(
        self,
        scope: str,
        quota: int,
        window: str = "1m",
        retry_after: Optional[int] = None,
    ):
        """
        :param scope: 限流范围 (如 'ip' 或 'account')
        :param quota: 允许的请求数 (如 100)
        :param window: 时间窗口 (如 '1m'-分钟, '1h'-小时)
        :param retry_after: 重试等待秒数
        """
        headers = {"Retry-After": str(retry_after)} if retry_after else None
        super().__init__(
            error_code=RateLimitErrors.TOO_MANY_REQUESTS,
            detail=f"超过{scope}的速率限制 ({quota}/{window})",
            headers=headers,
            scope=scope,
            quota=quota,
            window=window,
            retry_after=retry_after,
        )


class ServerException(APIError):
    """500服务器异常 (对应ErrorDomain.INTERNAL)"""

    def __init__(
        self,
        message: str = "服务器内部错误",
        reference: Optional[str] = None,
        log_context: Optional[Dict] = None,
    ):
        """
        :param reference: 错误追踪ID (如 'ERR-1234')
        :param log_context: 需要记录到日志的上下文
        """
        super().__init__(
            error_code=SystemErrors.INTERNAL_SERVER_ERROR,
            detail=message,
            reference=reference,
        )
        self.log_context = log_context or {}


class ServiceUnavailableException(APIError):
    """503服务不可用异常 (对应ErrorDomain.INTERNAL)"""

    def __init__(
        self,
        service_name: str,
        estimated_recovery: Optional[int] = None,
        outage_reason: Optional[str] = None,
    ):
        """
        :param service_name: 受影响的服务名 (如 'database')
        :param estimated_recovery: 预估恢复时间(秒)
        :param outage_reason: 中断原因 (内部使用)
        """
        headers = {}
        if estimated_recovery:
            headers["Retry-After"] = str(estimated_recovery)

        super().__init__(
            error_code=SystemErrors.SERVICE_UNAVAILABLE,
            detail=f"{service_name}服务暂时不可用",
            headers=headers,
            service=service_name,
            estimated_recovery=estimated_recovery,
            _internal_reason=outage_reason,  # 该字段会被自动过滤
        )


# ===== RAG项目特有异常 =====
class DocumentProcessingError(APIError):
    """文档处理异常"""

    def __init__(
        self,
        message: str = "文档处理失败",
        file_name: Optional[str] = None,
        file_type: Optional[str] = None,
        error_details: Optional[str] = None,
    ):
        super().__init__(
            error_code=RAGErrors.DOCUMENT_PROCESSING_FAILED,
            detail=message,
            file_name=file_name,
            file_type=file_type,
            error_details=error_details,
        )


class EmbeddingError(APIError):
    """嵌入生成异常"""

    def __init__(
        self,
        message: str = "嵌入生成失败",
        model_name: Optional[str] = None,
        text_length: Optional[int] = None,
    ):
        super().__init__(
            error_code=RAGErrors.EMBEDDING_GENERATION_FAILED,
            detail=message,
            model_name=model_name,
            text_length=text_length,
        )


class VectorSearchError(APIError):
    """向量搜索异常"""

    def __init__(
        self,
        message: str = "向量搜索失败",
        query: Optional[str] = None,
        index_name: Optional[str] = None,
    ):
        super().__init__(
            error_code=RAGErrors.VECTOR_SEARCH_FAILED,
            detail=message,
            query=query,
            index_name=index_name,
        )


class KnowledgeBaseError(APIError):
    """知识库异常"""

    def __init__(
        self,
        message: str = "知识库不存在",
        kb_id: Optional[str] = None,
        kb_name: Optional[str] = None,
    ):
        super().__init__(
            error_code=RAGErrors.KNOWLEDGE_BASE_NOT_FOUND,
            detail=message,
            kb_id=kb_id,
            kb_name=kb_name,
        )


class RetrievalError(APIError):
    """检索异常"""

    def __init__(
        self,
        message: str = "检索失败",
        query: Optional[str] = None,
        retrieval_type: Optional[str] = None,
    ):
        super().__init__(
            error_code=RAGErrors.RETRIEVAL_FAILED,
            detail=message,
            query=query,
            retrieval_type=retrieval_type,
        )


class GenerationError(APIError):
    """生成异常"""

    def __init__(
        self,
        message: str = "生成失败",
        model_name: Optional[str] = None,
        prompt_length: Optional[int] = None,
    ):
        super().__init__(
            error_code=RAGErrors.GENERATION_FAILED,
            detail=message,
            model_name=model_name,
            prompt_length=prompt_length,
        )


class ContextTooLargeError(APIError):
    """上下文过大异常"""

    def __init__(
        self,
        message: str = "上下文长度超出限制",
        current_length: Optional[int] = None,
        max_length: Optional[int] = None,
    ):
        super().__init__(
            error_code=RAGErrors.CONTEXT_TOO_LARGE,
            detail=message,
            current_length=current_length,
            max_length=max_length,
        )


# ===== 外部服务异常 =====
class ExternalServiceError(APIError):
    """外部服务异常"""

    def __init__(
        self,
        message: str = "外部服务不可用",
        service_name: Optional[str] = None,
        status_code: Optional[int] = None,
    ):
        super().__init__(
            error_code=ExternalServiceErrors.SERVICE_UNAVAILABLE,
            detail=message,
            service_name=service_name,
            external_status_code=status_code,
        )


# ===== 详细的外部服务异常类型 =====
class ExternalApiRateLimitedError(APIError):
    """外部API限流异常"""

    def __init__(
        self,
        api_name: str,
        retry_after: Optional[int] = None,
        message: Optional[str] = None,
    ):
        if not message:
            message = f"外部API {api_name} 请求被限流"
            if retry_after:
                message += f" (重试等待: {retry_after}s)"
        super().__init__(
            error_code=ExternalServiceErrors.API_RATE_LIMITED,
            detail=message,
            api_name=api_name,
            retry_after=retry_after,
        )


class ExternalInvalidResponseError(APIError):
    """外部服务无效响应异常"""

    def __init__(
        self,
        service_name: str,
        response_data: Optional[str] = None,
        expected_format: Optional[str] = None,
        message: Optional[str] = None,
    ):
        if not message:
            message = f"外部服务 {service_name} 返回无效响应"
            if expected_format:
                message += f" (期望格式: {expected_format})"
        super().__init__(
            error_code=ExternalServiceErrors.INVALID_RESPONSE,
            detail=message,
            service_name=service_name,
            response_data=(
                response_data[:200] + "..."
                if response_data and len(response_data) > 200
                else response_data
            ),
            expected_format=expected_format,
        )


class ExternalTimeoutError(APIError):
    """外部服务超时异常"""

    def __init__(
        self,
        service_name: str,
        timeout_seconds: int,
        operation: Optional[str] = None,
        message: Optional[str] = None,
    ):
        if not message:
            message = f"外部服务 {service_name} 请求超时: {timeout_seconds}s"
            if operation:
                message += f" (操作: {operation})"
        super().__init__(
            error_code=ExternalServiceErrors.TIMEOUT,
            detail=message,
            service_name=service_name,
            timeout_seconds=timeout_seconds,
            operation=operation,
        )


class ExternalAuthenticationFailedError(APIError):
    """外部服务认证失败异常"""

    def __init__(
        self,
        service_name: str,
        auth_type: Optional[str] = None,
        message: Optional[str] = None,
    ):
        if not message:
            message = f"外部服务 {service_name} 认证失败"
            if auth_type:
                message += f" (认证类型: {auth_type})"
        super().__init__(
            error_code=ExternalServiceErrors.AUTHENTICATION_FAILED,
            detail=message,
            service_name=service_name,
            auth_type=auth_type,
        )


class IntegrationError(APIError):
    """集成异常"""

    def __init__(
        self,
        message: str = "外部API调用失败",
        api_name: Optional[str] = None,
        error_response: Optional[str] = None,
    ):
        super().__init__(
            error_code=IntegrationErrors.EXTERNAL_API_FAILURE,
            detail=message,
            api_name=api_name,
            error_response=error_response,
        )


# ===== RAG项目缺失的异常类型 =====
class DocumentNotIndexedError(APIError):
    """文档未索引异常"""

    def __init__(
        self,
        document_id: str,
        document_name: Optional[str] = None,
        message: Optional[str] = None,
    ):
        if not message:
            message = f"文档未建立索引: {document_id}"
            if document_name:
                message += f" ({document_name})"
        super().__init__(
            error_code=RAGErrors.DOCUMENT_NOT_INDEXED,
            detail=message,
            document_id=document_id,
            document_name=document_name,
        )


if __name__ == "__main__":
    # 方式1：直接使用ErrorCode
    print("=== 直接使用ErrorCode ===")
    api_error = APIError(
        error_code=BusinessErrors.INVALID_OPERATION,
        detail="订单状态不允许支付",
        current_status="已取消",
    )
    print(f"Status: {api_error.status_code}")
    print(f"Detail: {api_error.detail}")
    print()

    # 方式2：使用快捷类
    print("=== 使用快捷异常类 ===")
    auth_error = UnauthorizedError("访问令牌已过期")
    print(f"Auth Error: {auth_error.detail}")

    not_found_error = NotFoundError("用户", "12345")
    print(f"NotFound Error: {not_found_error.detail}")

    forbidden_error = ForbiddenError("无权限访问此资源", "read_user")
    print(f"Forbidden Error: {forbidden_error.detail}")
    print()

    # 方式3：RAG项目特有异常
    print("=== RAG项目异常 ===")
    doc_error = DocumentProcessingError("PDF解析失败", file_name="document.pdf", file_type="pdf")
    print(f"Document Error: {doc_error.detail}")

    embedding_error = EmbeddingError("文本嵌入生成超时", model_name="text-embedding-ada-002")
    print(f"Embedding Error: {embedding_error.detail}")

    vector_error = VectorSearchError(
        "向量数据库连接失败", query="什么是机器学习", index_name="knowledge_base"
    )
    print(f"Vector Search Error: {vector_error.detail}")
    print()

    # 方式4：数据库和外部服务异常
    print("=== 数据库和外部服务异常 ===")
    db_error = DatabaseError("查询超时", "SELECT * FROM users")
    print(f"Database Error: {db_error.detail}")

    external_error = ExternalServiceError(
        "OpenAI API调用失败", service_name="openai", status_code=429
    )
    print(f"External Service Error: {external_error.detail}")

    rate_limit_error = RateLimitException(
        scope="api_key", quota=1000, window="1h", retry_after=3600
    )
    print(f"Rate Limit Error: {rate_limit_error.detail}")
