"""
IAM 策略模型

包含权限策略等 ABAC 相关实体
"""

from typing import Optional

from sqlalchemy import Index, Integer, Text, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column

from domain_common.models.base_model import Base
from domain_common.models.constants import JSONType
from domain_common.models.fields import Fields
from domain_common.models.mixins import AuditManager, AuditMixin, SoftDeleteMixin, StatusManager


class PermissionPolicy(Base, AuditMixin, SoftDeleteMixin):
    """权限策略模型

    支持基于属性的访问控制 (ABAC)，提供动态权限策略配置
    """

    __tablename__ = "permission_policies"

    # 重写主键为 policy_id
    policy_id: Mapped[str] = Fields.uuid_primary_key(doc="策略ID")

    # 多租户字段
    tenant_id: Mapped[str] = Fields.tenant()

    # 基本信息字段
    policy_name: Mapped[str] = Fields.name(doc="策略名称")
    policy_code: Mapped[str] = Fields.code(doc="策略编码")
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True, doc="策略描述")

    # 策略效果和优先级字段
    effect: Mapped[str] = Fields.code(max_length=10, doc="策略效果: allow, deny")
    priority: Mapped[int] = mapped_column(
        Integer, default=100, doc="策略优先级，数值越小优先级越高"
    )

    # 策略配置字段（JSONB）
    conditions: Mapped[JSONType] = Fields.json_field(default=[], doc="策略条件，JSON数组格式")
    actions: Mapped[JSONType] = Fields.json_field(default=[], doc="允许的操作，JSON数组格式")
    resources: Mapped[JSONType] = Fields.json_field(default=[], doc="资源范围，JSON数组格式")
    meta_data: Mapped[Optional[JSONType]] = Fields.json_field(default={}, doc="策略元数据")

    # 状态字段
    status: Mapped[str] = Fields.status()

    __table_args__ = (
        UniqueConstraint("tenant_id", "policy_code", name="uq_permission_policies_tenant_code"),
        Index("idx_permission_policies_tenant_effect", "tenant_id", "effect"),
        Index("idx_permission_policies_priority", "priority"),
        Index("idx_permission_policies_tenant_status", "tenant_id", "status"),
    )

    @property
    def status_manager(self) -> StatusManager:
        """获取状态管理器"""
        return StatusManager(self)

    @property
    def audit_manager(self) -> AuditManager:
        """获取审计管理器"""
        return AuditManager(self)

    def __repr__(self) -> str:
        return (f"<PermissionPolicy(policy_id={self.policy_id}, "
                f"policy_code={self.policy_code}, tenant_id={self.tenant_id})>")
