import asyncio
from datetime import datetime, timedelta
from typing import Any, Awaitable, Callable, Dict, Optional

from commonlib.core.tsif_logging import app_logger


class HeartbeatManager:
    """
    心跳管理器，负责定期检查连接健康状态并在失败时自动重连
    """

    def __init__(
        self,
        name: str,
        interval: timedelta,
        max_retries: int,
        heartbeat_func: Callable[[], Awaitable[bool]],
        reconnect_func: Callable[[], Awaitable[bool]],
        *,
        backoff_base: int = 2,
        max_backoff: int = 300,
        restart_cooldown: int = 60,
        delay_after_restart_fail: int = 30,
    ):
        """
        初始化心跳管理器

        Args:
            name: 心跳任务名称
            interval: 基础心跳间隔
            max_retries: 最大连续失败次数
            heartbeat_func: 心跳检测函数，应返回 bool
            reconnect_func: 重启函数，应返回 bool
            backoff_base: 初始退避秒数（指数增长）
            max_backoff: 最大退避秒数
            restart_cooldown: 最小重启间隔秒数
            delay_after_restart_fail: 重启失败后的额外等待秒数
        """
        self._name = name
        self._interval = interval
        self._max_retries = max_retries
        self._heartbeat_func = heartbeat_func
        self._reconnect_func = reconnect_func

        self._last_success: Optional[bool] = None
        self._last_time: Optional[datetime] = None
        self._retry_count: int = 0
        self._task: Optional[asyncio.Task] = None
        self._running = False

        # 防抖控制
        self._backoff_base = backoff_base
        self._current_backoff = backoff_base
        self._max_backoff = max_backoff
        self._restart_cooldown = timedelta(seconds=restart_cooldown)
        self._delay_after_restart_fail = delay_after_restart_fail
        self._last_restart_time: Optional[datetime] = None

        # 指标统计
        self._total_heartbeats = 0
        self._failed_heartbeats = 0
        self._reconnect_attempts = 0
        self._successful_reconnects = 0

    async def start(self):
        """
        启动心跳监控任务
        """
        await self.stop()
        self._running = True
        self._task = asyncio.create_task(self._run_loop())
        app_logger.info(f"[{self._name}] Heartbeat manager started")

    async def stop(self, timeout: float = 5.0):
        """
        优雅停止心跳监控任务

        Args:
            timeout: 等待任务停止的超时时间（秒）
        """
        self._running = False
        if self._task:
            self._task.cancel()
            try:
                await asyncio.wait_for(self._task, timeout=timeout)
            except (asyncio.CancelledError, asyncio.TimeoutError):
                app_logger.warning(f"[{self._name}] Heartbeat task forcefully cancelled")
            except Exception as e:
                app_logger.error(f"[{self._name}] Error stopping heartbeat task: {e}")
            finally:
                self._task = None
            app_logger.info(f"[{self._name}] Heartbeat manager stopped")

    async def _run_loop(self):
        """
        心跳监控主循环
        """
        try:
            while self._running:
                start = datetime.now()
                try:
                    self._total_heartbeats += 1
                    success = await self._heartbeat_func()
                    self._last_success = success

                    if success:
                        self._retry_count = 0
                        self._current_backoff = self._backoff_base
                        self._last_time = datetime.now()
                        # app_logger.debug(f"[{self._name}] Heartbeat OK")
                    else:
                        self._failed_heartbeats += 1
                        self._retry_count += 1
                        app_logger.warning(
                            f"[{self._name}] Heartbeat failed ({self._retry_count}/{self._max_retries})"
                        )

                        if self._retry_count >= self._max_retries:
                            await self._handle_reconnect()

                        # 使用可中断的退避等待
                        if not await self._interruptible_sleep(self._current_backoff):
                            break
                        self._current_backoff = min(self._current_backoff * 2, self._max_backoff)
                        continue  # 跳过标准 sleep

                except Exception as e:
                    self._last_success = False
                    self._retry_count += 1
                    self._failed_heartbeats += 1
                    app_logger.exception(f"[{self._name}] Heartbeat exception: {e}")

                # 标准间隔等待
                elapsed = (datetime.now() - start).total_seconds()
                sleep_time = max(1.0, self._interval.total_seconds() - elapsed)
                if not await self._interruptible_sleep(sleep_time):
                    break

        except asyncio.CancelledError:
            app_logger.info(f"[{self._name}] Heartbeat task cancelled")
        except Exception as e:
            app_logger.exception(f"[{self._name}] Unexpected error in heartbeat loop: {e}")

    async def _handle_reconnect(self):
        """
        处理重连逻辑
        """
        now = datetime.now()
        if not self._last_restart_time or now - self._last_restart_time >= self._restart_cooldown:
            app_logger.warning(f"[{self._name}] Triggering reconnect...")
            self._reconnect_attempts += 1

            try:
                success = await self._reconnect_func()
                self._last_restart_time = now

                if success:
                    self._successful_reconnects += 1
                    self._retry_count = 0
                    self._current_backoff = self._backoff_base
                    app_logger.info(f"[{self._name}] Reconnect successful")
                else:
                    app_logger.warning(
                        f"[{self._name}] Reconnect failed, sleeping {self._delay_after_restart_fail}s"
                    )
                    await self._interruptible_sleep(self._delay_after_restart_fail)
            except Exception as e:
                app_logger.error(f"[{self._name}] Reconnect exception: {e}")
                await self._interruptible_sleep(self._delay_after_restart_fail)
        else:
            remaining = (self._restart_cooldown - (now - self._last_restart_time)).seconds
            app_logger.warning(
                f"[{self._name}] Reconnect suppressed due to cooldown ({remaining}s left)"
            )

    async def _interruptible_sleep(self, duration: float) -> bool:
        """
        可中断的睡眠函数

        Args:
            duration: 睡眠时长（秒）

        Returns:
            bool: True 如果正常完成睡眠，False 如果被中断
        """
        try:
            # 每秒检查一次 _running 状态
            for _ in range(int(duration)):
                if not self._running:
                    return False
                await asyncio.sleep(1)

            # 处理剩余的小数部分
            remaining = duration - int(duration)
            if remaining > 0 and self._running:
                await asyncio.sleep(remaining)

            return self._running
        except asyncio.CancelledError:
            return False

    def is_healthy(self) -> bool:
        """
        检查连接是否健康

        Returns:
            bool: True 如果连接健康，False 否则
        """
        if not self._last_success or not self._last_time:
            return False
        return datetime.now() - self._last_time < self._interval * 1.5

    def get_status(self) -> Dict[str, Any]:
        """
        获取心跳管理器状态信息

        Returns:
            Dict[str, Any]: 包含状态信息的字典
        """
        return {
            "name": self._name,
            "running": self._running,
            "healthy": self.is_healthy(),
            "last_heartbeat": self._last_time.isoformat() if self._last_time else None,
            "heartbeat_success": self._last_success,
            "retry_count": self._retry_count,
            "current_backoff": self._current_backoff,
            "last_restart_time": (
                self._last_restart_time.isoformat() if self._last_restart_time else None
            ),
            "metrics": self.get_metrics(),
        }

    def get_metrics(self) -> Dict[str, int]:
        """
        获取心跳指标统计

        Returns:
            Dict[str, int]: 包含指标统计的字典
        """
        return {
            "total_heartbeats": self._total_heartbeats,
            "failed_heartbeats": self._failed_heartbeats,
            "success_rate": (
                round(
                    (self._total_heartbeats - self._failed_heartbeats)
                    / self._total_heartbeats
                    * 100,
                    2,
                )
                if self._total_heartbeats > 0
                else 0.0
            ),
            "reconnect_attempts": self._reconnect_attempts,
            "successful_reconnects": self._successful_reconnects,
            "reconnect_success_rate": (
                round(self._successful_reconnects / self._reconnect_attempts * 100, 2)
                if self._reconnect_attempts > 0
                else 0.0
            ),
        }

    def reset_metrics(self):
        """
        重置指标统计
        """
        self._total_heartbeats = 0
        self._failed_heartbeats = 0
        self._reconnect_attempts = 0
        self._successful_reconnects = 0
        app_logger.info(f"[{self._name}] Metrics reset")
