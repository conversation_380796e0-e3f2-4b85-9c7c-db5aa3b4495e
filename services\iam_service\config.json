{"app_name": "iam-service", "config_path": "./config/config_prd.json", "basic_log_dir": "../../../log", "debug": true, "application": {"debug": true, "title": "IAM Service API", "project_name": "IAM Service", "description": "用户与权限管理服务", "secret_key": "your-production-secret-key-here", "docs_url": "/docs", "openapi_url": "/openapi.json", "redoc_url": "/redoc"}, "connection_priority": {"redis": {"enabled": true}, "postgres": {"enabled": true}, "faststream": {"enabled": true}}, "persistence": {"mysql": {"MYSQL_HOST": "**************", "MYSQL_PORT": 3306, "MYSQL_USERNAME": "root", "MYSQL_PASSWORD": "thismore@123456", "MYSQL_DATABASE": "thingsmore", "MYSQL_SCHEME": "mysql+aiomysql", "MYSQL_POOL_SIZE": 30, "MYSQL_TIMEOUT": 10, "MYSQL_POOL_RECYCLE": 3600, "MYSQL_POOL_PRE_PING": true, "MYSQL_ECHO": false}, "postgres": {"POSTGRES_HOST": "**************", "POSTGRES_PORT": 5432, "POSTGRES_USER": "postgres", "POSTGRES_PASSWORD": "thismore@123456", "POSTGRES_DATABASE": "thingsmore", "POSTGRES_SCHEMA": "public", "POSTGRES_POOL_SIZE": 30, "POSTGRES_POOL_TIMEOUT": 10, "POSTGRES_POOL_RECYCLE": 3600, "POSTGRES_POOL_PRE_PING": true, "POSTGRES_ECHO": false}, "redis": {"REDIS_HOST": "**************", "REDIS_PORT": 6378, "REDIS_DB": 0, "REDIS_USERNAME": "", "REDIS_PASSWORD": "thismore@123456", "REDIS_SSL": false, "REDIS_POOL_SIZE": 10, "REDIS_MAX_CONNECTIONS": 10, "REDIS_POOL_TIMEOUT": 5, "REDIS_POOL_RECYCLE": 3600, "REDIS_RETRY_ON_TIMEOUT": true, "REDIS_POOL_PRE_PING": true, "REDIS_DECODE_RESPONSE": true}, "mongodb": {"MONGODB_HOST": "**************", "MONGODB_PORT": 27017, "MONGODB_USERNAME": "root", "MONGODB_PASSWORD": "thismore@123456", "MONGODB_DATABASE": "thingsmore", "MONGODB_AUTH_SOURCE": "admin", "MONGODB_AUTH_MECHANISM": "SCRAM-SHA-256", "MONGODB_MIN_POOL_SIZE": 10, "MONGODB_MAX_POOL_SIZE": 50, "MONGODB_MAX_IDLE_TIME_MS": 10000, "MONGODB_CONNECT_TIMEOUT_MS": 20000}, "faststream": {"FAST_STREAM_BROKER_URL": "redis://:thismore%40123456@**************:6378/1"}}, "middleware": {"cors": {"AllowOrigins": ["*"], "AllowMethods": ["*"], "AllowHeaders": ["*"], "AllowCredentials": true, "ExposeHeaders": [], "MaxAge": 600}, "security": {"SSLRedirect": false, "ForceSSL": false, "FrameDeny": true, "ContentTypeNosniff": true, "BrowserXSSFilter": true, "HSTS": {"IncludeSubdomains": true, "Preload": false, "MaxAge": 31536000}}, "compression": {"Enabled": true, "Level": 9, "MinimumSize": 500}}, "iam_config": {"security": {"jwt_secret_key": "your-secret-key-here", "jwt_algorithm": "HS256", "access_token_expire_minutes": 120, "refresh_token_expire_days": 7, "password_hash_rounds": 12}, "cache": {"permissions_ttl": 3600, "roles_ttl": 7200, "policies_ttl": 1800, "user_info_ttl": 1800}, "rate_limit": {"login_per_minute": 5, "api_per_second": 100, "batch_per_hour": 10}}}