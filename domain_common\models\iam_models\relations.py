"""
IAM 关联关系模型

包含用户角色、角色权限、用户策略等关联关系实体
"""

from datetime import date, datetime
from typing import Optional

from sqlalchemy import Date, Index, String, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column

from domain_common.models.base_model import Base
from domain_common.models.constants import AssignmentType
from domain_common.models.fields import Fields
from domain_common.models.mixins import SoftDeleteMixin


class UserRole(Base, SoftDeleteMixin):
    """用户角色关联模型

    管理用户与角色的多对多关联关系，支持临时角色分配
    """

    __tablename__ = "user_roles"

    # 使用 BIGSERIAL 作为主键
    id: Mapped[int] = Fields.bigint_id(doc="关联记录ID")

    # 多租户字段
    tenant_id: Mapped[str] = Fields.tenant()

    # 关联字段
    user_id: Mapped[str] = Fields.user(required=True)
    role_id: Mapped[str] = mapped_column(String(64), nullable=False, doc="角色ID")

    # 分配类型和时间字段
    assignment_type: Mapped[str] = Fields.code(max_length=20, doc="分配类型: permanent, temporary")
    effective_date: Mapped[Optional[date]] = mapped_column(Date, nullable=True, doc="生效日期")
    expiry_date: Mapped[Optional[date]] = mapped_column(Date, nullable=True, doc="过期日期")

    # 状态字段
    status: Mapped[str] = Fields.status()

    # 分配信息字段
    assigned_at: Mapped[datetime] = Fields.created_at()
    assigned_by: Mapped[Optional[str]] = Fields.user(required=False)

    __table_args__ = (
        UniqueConstraint("tenant_id", "user_id", "role_id", name="uq_user_roles_tenant_user_role"),
        Index("idx_user_roles_tenant_user", "tenant_id", "user_id"),
        Index("idx_user_roles_tenant_role", "tenant_id", "role_id"),
        Index("idx_user_roles_status", "status"),
        Index("idx_user_roles_expiry", "expiry_date"),
    )

    def __repr__(self) -> str:
        return (f"<UserRole(id={self.id}, user_id={self.user_id}, "
                f"role_id={self.role_id}, tenant_id={self.tenant_id})>")


class RolePermission(Base, SoftDeleteMixin):
    """角色权限关联模型

    管理角色与权限的多对多关联关系，RBAC 的核心关联
    """

    __tablename__ = "role_permissions"

    # 使用 BIGSERIAL 作为主键
    id: Mapped[int] = Fields.bigint_id(doc="关联记录ID")

    # 多租户字段
    tenant_id: Mapped[str] = Fields.tenant()

    # 关联字段
    role_id: Mapped[str] = mapped_column(String(64), nullable=False, doc="角色ID")
    permission_id: Mapped[str] = mapped_column(String(64), nullable=False, doc="权限ID")

    # 状态字段
    status: Mapped[str] = Fields.status()

    # 分配信息字段
    assigned_at: Mapped[datetime] = Fields.created_at()
    assigned_by: Mapped[Optional[str]] = Fields.user(required=False)

    __table_args__ = (
        UniqueConstraint(
            "tenant_id",
            "role_id",
            "permission_id",
            name="uq_role_permissions_tenant_role_permission",
        ),
        Index("idx_role_permissions_tenant_role", "tenant_id", "role_id"),
        Index("idx_role_permissions_tenant_permission", "tenant_id", "permission_id"),
        Index("idx_role_permissions_status", "status"),
    )

    def __repr__(self) -> str:
        return (f"<RolePermission(id={self.id}, "
                f"role_id={self.role_id}, permission_id={self.permission_id}, tenant_id={self.tenant_id})>")


class UserPolicy(Base, SoftDeleteMixin):
    """用户策略关联模型

    管理用户与权限策略的关联关系，支持 ABAC 扩展
    """

    __tablename__ = "user_policies"

    # 使用 BIGSERIAL 作为主键
    id: Mapped[int] = Fields.bigint_id(doc="关联记录ID")

    # 多租户字段
    tenant_id: Mapped[str] = Fields.tenant()

    # 关联字段
    user_id: Mapped[str] = Fields.user(required=True)
    policy_id: Mapped[str] = mapped_column(String(64), nullable=False, doc="策略ID")

    # 分配类型和时间字段
    assignment_type: Mapped[str] = Fields.code(max_length=20, doc="分配类型: permanent, temporary")
    effective_date: Mapped[Optional[date]] = mapped_column(Date, nullable=True, doc="生效日期")
    expiry_date: Mapped[Optional[date]] = mapped_column(Date, nullable=True, doc="过期日期")

    # 状态字段
    status: Mapped[str] = Fields.status()

    # 分配信息字段
    assigned_at: Mapped[datetime] = Fields.created_at()
    assigned_by: Mapped[Optional[str]] = Fields.user(required=False)

    __table_args__ = (
        UniqueConstraint(
            "tenant_id",
            "user_id",
            "policy_id",
            name="uq_user_policies_tenant_user_policy",
        ),
        Index("idx_user_policies_tenant_user", "tenant_id", "user_id"),
        Index("idx_user_policies_tenant_policy", "tenant_id", "policy_id"),
        Index("idx_user_policies_status", "status"),
        Index("idx_user_policies_expiry", "expiry_date"),
    )

    def __repr__(self) -> str:
        return (f"<UserPolicy(id={self.id}, "
                f"user_id={self.user_id}, policy_id={self.policy_id}, tenant_id={self.tenant_id})>")


class UserGroupMember(Base):
    """用户组成员关联模型

    管理用户与用户组的多对多关联关系，支持临时成员分配
    """

    __tablename__ = "user_group_members"

    # 使用 BIGSERIAL 作为主键
    id: Mapped[int] = Fields.bigint_id(doc="关联记录ID")

    # 多租户字段
    tenant_id: Mapped[str] = Fields.tenant()

    # 关联字段
    user_id: Mapped[str] = Fields.user(required=True)
    group_id: Mapped[str] = mapped_column(String(64), nullable=False, doc="用户组ID")

    # 分配类型和时间字段
    assignment_type: Mapped[str] = mapped_column(
        String(20),
        nullable=False,
        default=AssignmentType.PERMANENT,
        doc="分配类型: permanent, temporary, inherited",
    )
    effective_date: Mapped[Optional[date]] = mapped_column(Date, nullable=True, doc="生效日期")
    expiry_date: Mapped[Optional[date]] = mapped_column(Date, nullable=True, doc="过期日期")

    # 状态字段
    status: Mapped[str] = Fields.status()

    # 分配信息字段
    assigned_at: Mapped[datetime] = Fields.created_at()
    assigned_by: Mapped[Optional[str]] = Fields.user(required=False)

    __table_args__ = (
        UniqueConstraint(
            "tenant_id",
            "user_id",
            "group_id",
            name="uq_user_group_members_tenant_user_group",
        ),
        Index("idx_user_group_members_tenant_user", "tenant_id", "user_id"),
        Index("idx_user_group_members_tenant_group", "tenant_id", "group_id"),
        Index("idx_user_group_members_status", "status"),
        Index("idx_user_group_members_expiry", "expiry_date"),
    )

    def __repr__(self) -> str:
        return (f"<UserGroupMember(id={self.id}, "
                f"user_id={self.user_id}, group_id={self.group_id}, tenant_id={self.tenant_id})>")


class UserGroupRole(Base):
    """用户组角色关联模型

    管理用户组与角色的多对多关联关系，实现权限复用
    """

    __tablename__ = "user_group_roles"

    # 使用 BIGSERIAL 作为主键
    id: Mapped[int] = Fields.bigint_id(doc="关联记录ID")

    # 多租户字段
    tenant_id: Mapped[str] = Fields.tenant()

    # 关联字段
    group_id: Mapped[str] = mapped_column(String(64), nullable=False, doc="用户组ID")
    role_id: Mapped[str] = mapped_column(String(64), nullable=False, doc="角色ID")

    # 分配类型和时间字段
    assignment_type: Mapped[str] = mapped_column(
        String(20),
        nullable=False,
        default=AssignmentType.PERMANENT,
        doc="分配类型: permanent, temporary",
    )
    effective_date: Mapped[Optional[date]] = mapped_column(Date, nullable=True, doc="生效日期")
    expiry_date: Mapped[Optional[date]] = mapped_column(Date, nullable=True, doc="过期日期")

    # 状态字段
    status: Mapped[str] = Fields.status()

    # 分配信息字段
    assigned_at: Mapped[datetime] = Fields.created_at()
    assigned_by: Mapped[Optional[str]] = Fields.user(required=False)

    __table_args__ = (
        UniqueConstraint(
            "tenant_id",
            "group_id",
            "role_id",
            name="uq_user_group_roles_tenant_group_role",
        ),
        Index("idx_user_group_roles_tenant_group", "tenant_id", "group_id"),
        Index("idx_user_group_roles_tenant_role", "tenant_id", "role_id"),
        Index("idx_user_group_roles_status", "status"),
        Index("idx_user_group_roles_expiry", "expiry_date"),
    )

    def __repr__(self) -> str:
        return (f"<UserGroupRole(id={self.id}, "
                f"group_id={self.group_id}, role_id={self.role_id}, tenant_id={self.tenant_id})>")


class UserGroupPermission(Base):
    """用户组权限关联模型

    管理用户组与权限的多对多关联关系，支持直接权限分配
    """

    __tablename__ = "user_group_permissions"

    # 使用 BIGSERIAL 作为主键
    id: Mapped[int] = Fields.bigint_id(doc="关联记录ID")

    # 多租户字段
    tenant_id: Mapped[str] = Fields.tenant()

    # 关联字段
    group_id: Mapped[str] = mapped_column(String(64), nullable=False, doc="用户组ID")
    permission_id: Mapped[str] = mapped_column(String(64), nullable=False, doc="权限ID")

    # 分配类型和时间字段
    assignment_type: Mapped[str] = mapped_column(
        String(20),
        nullable=False,
        default=AssignmentType.PERMANENT,
        doc="分配类型: permanent, temporary",
    )
    effective_date: Mapped[Optional[date]] = mapped_column(Date, nullable=True, doc="生效日期")
    expiry_date: Mapped[Optional[date]] = mapped_column(Date, nullable=True, doc="过期日期")

    # 状态字段
    status: Mapped[str] = Fields.status()

    # 分配信息字段
    assigned_at: Mapped[datetime] = Fields.created_at()
    assigned_by: Mapped[Optional[str]] = Fields.user(required=False)

    __table_args__ = (
        UniqueConstraint(
            "tenant_id",
            "group_id",
            "permission_id",
            name="uq_user_group_permissions_tenant_group_permission",
        ),
        Index("idx_user_group_permissions_tenant_group", "tenant_id", "group_id"),
        Index("idx_user_group_permissions_tenant_permission", "tenant_id", "permission_id"),
        Index("idx_user_group_permissions_status", "status"),
        Index("idx_user_group_permissions_expiry", "expiry_date"),
    )

    def __repr__(self) -> str:
        return (f"<UserGroupPermission(id={self.id}, "
                f"group_id={self.group_id}, permission_id={self.permission_id}, tenant_id={self.tenant_id})>")
