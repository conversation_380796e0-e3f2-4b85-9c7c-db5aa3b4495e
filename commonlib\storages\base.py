import asyncio
from abc import ABC, abstractmethod
from asyncio import Lock
from dataclasses import dataclass
from datetime import timedelta
from enum import Enum, auto
from typing import Any, Dict, Generic, Optional, Type, TypeVar

from pydantic import BaseModel, ValidationError

from commonlib.core.tsif_logging import app_logger
from commonlib.storages.heartbeat_manager import HeartbeatManager

# 泛型类型定义
TConfig = TypeVar("TConfig", bound=BaseModel)
TConnection = TypeVar("TConnection")


@dataclass
class ConnectorParams:
    """可热更新的运行时参数"""

    max_retries: int = 3
    enable_heartbeat: bool = True
    heartbeat_interval: timedelta = timedelta(seconds=30)


class ConnectorStatus(Enum):
    INIT = auto()
    CONNECTING = auto()
    CONNECTED = auto()
    DISCONNECTED = auto()
    ERROR = auto()
    STOPPED = auto()


class ConnectorStateManager:
    def __init__(self, name: str) -> None:
        self._status: ConnectorStatus = ConnectorStatus.INIT
        self._lock = asyncio.Lock()
        self._name = name

    @property
    def status(self) -> ConnectorStatus:
        return self._status

    async def set_status(self, new_status: ConnectorStatus) -> None:
        async with self._lock:
            if new_status != self._status:
                old = self._status
                self._status = new_status
                app_logger.debug(f"[{self._name}] Status changed: {old.name} → {new_status.name}")


class BaseConnector(ABC, Generic[TConnection, TConfig]):
    """支持完整热更新的连接器基类"""

    _config_class: Type[TConfig] = None

    def __init__(
        self,
        initial_params: Optional[ConnectorParams] = None,
    ):
        # 初始化日志和状态
        self.__lock = Lock()
        self.__active = False
        # 参数管理
        self.__params = initial_params or ConnectorParams()
        # 心跳检测
        self.__heartbeat_manager: Optional[HeartbeatManager] = None
        # 连接配置
        self.__config: Optional[TConfig] = None
        # 核心连接
        self._connection: Optional[TConnection] = None
        # 链接状态
        self.__state = ConnectorStateManager(name=self.name)

    @classmethod
    def validate_config(cls, config: Dict):
        return cls._config_class(**config)

    # === 必须实现的抽象方法 ===
    @property
    @abstractmethod
    def name(self) -> str:
        """连接器名称标识"""
        raise NotImplementedError

    @abstractmethod
    async def _connect(self) -> bool:
        """
        建立连接的具体实现
        """
        raise NotImplementedError

    @abstractmethod
    async def _close(self) -> None:
        """关闭连接的具体实现"""
        raise NotImplementedError

    @abstractmethod
    async def _perform_heartbeat_check(self) -> bool:
        """
        实际心跳检测逻辑
        try:
            async with self._connection.cursor() as cursor:
                await cursor.execute("SELECT 1")  # 简单查询测试
                return True
        except Exception:
            return False
        :return:
        """
        raise NotImplementedError

    @property
    def config_class(self) -> Type[TConfig]:
        return self._config_class

    # === 核心功能方法 ===
    @property
    def is_connected(self) -> bool:
        """线程安全的连接状态检查"""
        return (
            self.__active
            and self._connection is not None
            and (self.__heartbeat_manager.is_healthy() if self.__heartbeat_manager else True)
        )

    def __repr__(self):
        return f"<Connector {self.name}, status={self.__state.status.name}>"

    async def connect(self) -> bool:
        app_logger.info(f"[{self.name}] Attempting to connect...")
        await self.__state.set_status(ConnectorStatus.CONNECTING)
        try:
            self.__active = await self._connect()
            if self.__active:
                await self.__state.set_status(ConnectorStatus.CONNECTED)
            else:
                await self.__state.set_status(ConnectorStatus.ERROR)
                app_logger.error(f"[{self.name}] Connection failed.")
            return self.__active
        except Exception as e:
            self.__active = False
            await self.__state.set_status(ConnectorStatus.ERROR)
            app_logger.error(f"[{self.name}] Connection with Uncached error. {e}")
            return False

    async def start(self) -> None:
        """启动连接器与心跳任务"""
        await self.connect()
        # 注意，即使创建失败，这里也需要构建一个后台任务
        if self.__params.enable_heartbeat and not self.__heartbeat_manager:
            self.__heartbeat_manager = HeartbeatManager(
                name=self.name,
                interval=self.__params.heartbeat_interval,
                max_retries=self.__params.max_retries,
                heartbeat_func=self._perform_heartbeat_check,
                reconnect_func=self.safe_restart,
            )
            await self.__heartbeat_manager.start()
        else:
            app_logger.warning(
                f"[{self.name}] enable_heartbeat is {self.__params.enable_heartbeat}"
            )

    async def stop(self, fource_stop: bool = False):
        """安全停止并清理敏感数据"""
        app_logger.info(f"[{self.name}] Attempting to close...")

        if self.__heartbeat_manager:
            if fource_stop:
                await self.__heartbeat_manager.stop()
                self.__heartbeat_manager = None

        # 清理连接和敏感数据
        try:
            await self._close()
        except Exception as e:
            app_logger.error(f"[{self.name}] Connection cleanup failed: {str(e)}")
        finally:
            self.__active = False
            self._connection = None  # 显式置空
            await self.__state.set_status(ConnectorStatus.STOPPED)

    async def safe_restart(self, fource_stop: bool = False) -> bool:
        """原子化的安全重连（修复方法名拼写错误）"""
        async with self.__lock:
            try:
                app_logger.info(f"[{self.name}] Attempting safe restart")
                await self.stop(fource_stop=fource_stop)
                await self.start()

                return True
            except Exception as e:
                app_logger.error(
                    f"[{self.name}] Reconnect failed error:{str(e)}",
                    exception=True,
                )
                return False

    # === 配置和参数管理 ===
    def load_config(self, config: Dict[str, Any]) -> TConfig:
        """加载并验证配置"""
        try:
            new_config = self._config_class(**config)
            self.__config = new_config
            return new_config
        except ValidationError as e:
            app_logger.error(f"{self.name} configs validation failed: {e}")
            raise ValueError(f"Invalid {self.name} configs: {e}") from e
        except Exception as e:
            app_logger.error(f"[{self.name}] Config validation failed: {e}")
            raise

    async def reload(
        self,
        new_config: Optional[Dict[str, Any]] = None,
        new_params: Optional[ConnectorParams] = None,
    ) -> None:
        """
        原子化热更新（配置和参数）
        参数：
            new_config: 新配置字典（None表示不更新）
            new_params: 新运行时参数（None表示不更新）
        """

        async with self.__lock:
            # 记录旧状态
            config_changed = False

            try:
                # 更新配置
                if new_config is not None:
                    new_valid_config = self.load_config(new_config)
                    if self.__config != new_valid_config:
                        self.__config = new_valid_config
                        config_changed = True

                # 更新参数
                if new_params is not None and self.__params != new_params:
                    self.__params = new_params

                # 需要重建连接的情况
                if config_changed:
                    app_logger.info(f"[{self.name}] Config changed, reconnecting...")
                    await self.safe_restart(fource_stop=True)
            except Exception as e:
                app_logger.error(f"[{self.name}] Reload failed: {str(e)}")
                # 自动回滚参数版本
                raise

    def get_client(self) -> TConnection:
        if not self.is_connected:
            raise ConnectionError(f"[{self.name}] Connection not available")
        return self._connection

    @property
    def config(self) -> TConfig:
        return self.__config

    async def health_check(self) -> Dict[str, Any]:
        """返回连接健康状态"""
        return {
            "status": self.__state.status.name,
            "active": self.__active,
            **(self.__heartbeat_manager.get_status() if self.__heartbeat_manager else {}),
        }
