---
alwaysApply: false
---
# 📐 Cursor 模块架构规则文档 (.mdc 版本)

> 本文基于 Cursor 支持的 rules.json 格式，扩展为可读性更强的 `.mdc` 格式，包含模块依赖图、架构结构、代码风格与最佳实践。

---

## 🧱 架构与模块分层规则

### 🔗 模块依赖图（arch:dependency-graph）

- 模块依赖路径为：
  ```
  agent ← chat ← config/common/user
  ```
- 说明：核心模块依赖共享基础模块，避免循环引用，遵循清晰的上下层结构。  
- **补充说明**：为保证模块高内聚，模块内状态管理、服务和组件应完全封装，避免跨模块直接操作状态，仅通过事件总线通信。

```json
{
  "pattern": "src/modules/**/*",
  "tags": ["arch:dependency-graph"],
  "description": "模块依赖图：agent ← chat ← config/common/user，核心模块依赖共享基础模块，避免循环引用，遵循层次结构设计。模块内状态、服务、组件自包含，通信通过事件总线。"
}
```

---

### 🧩 模块分层架构（arch:layered, arch:modular）

每个业务模块都应包含以下目录：

```
modules/{module}/
├── components/  # UI 组件
├── services/    # API 服务与业务逻辑
├── stores/      # Zustand 状态管理
└── types/       # 类型定义
```

- **补充说明**：
  - 组件应保持单一职责，避免功能耦合过多  
  - 状态集中管理在 stores，组件通过调用 store 的 action 进行数据变更  
  - 服务层封装所有对后端 API 的调用，并统一错误处理  
  - 事件类型在模块内定义，模块间通信统一使用事件总线

```json
{
  "pattern": "src/modules/**/*",
  "tags": ["arch:layered", "arch:modular"],
  "description": "模块化分层架构：每个模块包含 components、services、stores、types 四层，统一命名与职责划分，利于扩展与维护。组件职责单一，状态由 store 管理，服务层封装 API，事件总线通信。"
}
```

---

## 🎨 代码风格规范

### 🌟 Clean Code + TypeScript + 模块化（style:*）

统一代码规范包括：

- 使用 TypeScript，确保类型安全  
- 遵循模块分层与职责单一  
- 使用 ESLint + Prettier 格式化  
- 命名一致性、组件语义化、逻辑清晰  

- **补充说明**：模块内代码应实现高内聚，避免组件直接调用外部模块状态或服务；组件 props 精简，职责单一；统一错误捕获并通过事件总线上报，避免组件内直接打印错误。

```json
{
  "pattern": "src/**/*",
  "tags": ["style:clean-code", "style:typescript", "style:modular"],
  "description": "代码风格规范：采用 TypeScript、模块分层、单一职责函数、命名一致性，遵循 Clean Code 与 ESLint + Prettier 规范。代码高内聚，组件职责单一，错误统一上报。"
}
```

---

## ✅ 最佳实践规范

### 1️⃣ 模块通信（best-practice:communication）

推荐使用事件总线 `emitEvent/onEvent` 进行模块通信：

```ts
// ✅ 推荐
emitEvent(EventTypes.AGENT_CREATED, { agent })

// ❌ 避免
import { useAgentStore } from '../modules/agent'
```

- **补充说明**：
  - 各模块定义并管理自己的事件类型，避免事件命名冲突  
  - 组件内部仅调用本模块 store/action，不跨模块访问状态  
  - 保持组件-服务-状态三角闭环，模块内代码自包含  

```json
{
  "pattern": "src/**/*",
  "tags": ["best-practice:communication"],
  "description": "模块间通信推荐：通过事件总线 emitEvent/onEvent 通信，避免跨模块直接访问 store。模块定义独立事件类型，组件调用本模块 store/action，保持模块高内聚。"
}
```

---

### 2️⃣ 组件设计（best-practice:component-design）

保持组件单一职责，接口清晰：

```ts
// ✅ 推荐
interface AgentCardProps {
  agent: Agent
  onEdit?: (agent: Agent) => void
  onDelete?: (agent: Agent) => void
}

// ❌ 避免
interface AgentCardProps {
  agent: Agent
  onEdit?: (agent: Agent) => void
  onDelete?: (agent: Agent) => void
  onChat?: (agent: Agent) => void
  onConfig?: (agent: Agent) => void
  // ... 过多职责
}
```

- **补充说明**：
  - 复杂组件应拆分成多个功能单一的子组件  
  - props 只传递必要数据，避免全量传递实体对象  
  - 组件不应承担过多业务逻辑，交由 store 或 service 处理  

```json
{
  "pattern": "src/**/*",
  "tags": ["best-practice:component-design"],
  "description": "组件设计推荐：保持组件职责单一，接口清晰，避免过度耦合多个功能 props。复杂组件拆分，props 精简，业务逻辑交由 store/service。"
}
```

---

### 3️⃣ 错误处理（best-practice:error-handling）

采用统一的错误捕获与事件上报机制：

```ts
// ✅ 推荐
try {
  const result = await agentService.createAgent(agentData)
  emitEvent(EventTypes.AGENT_CREATED, { agent: result })
} catch (error) {
  console.error('Failed to create agent:', error)
  emitEvent(EventTypes.ERROR_OCCURRED, { error })
}
```

- **补充说明**：
  - 所有 API 请求封装在 service 层，统一捕获错误  
  - 错误通过事件总线通知上层，方便全局统一处理和用户反馈  
  - 避免组件内直接打印或捕获错误，确保异常管理集中化  

```json
{
  "pattern": "src/**/*",
  "tags": ["best-practice:error-handling"],
  "description": "统一错误处理机制：使用 try/catch + emitEvent(EventTypes.ERROR_OCCURRED, ...) 上报异常。错误捕获集中在 service 层，组件不直接处理异常。"
}
```
