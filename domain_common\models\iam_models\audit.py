"""
IAM 审计模型

包含审计日志等安全审计相关实体

注意：AuditLog 使用 session_context 存储会话相关信息，而不是直接的 session_id
"""

from datetime import datetime
from typing import Any, Dict, Optional

from sqlalchemy import Index, String, Text
from sqlalchemy.orm import Mapped, mapped_column

from domain_common.models.base_model import Base
from domain_common.models.constants import JSONType
from domain_common.models.fields import Fields


class AuditLog(Base):
    """审计日志模型

    记录系统中的所有重要操作，用于安全审计和合规性

    ⚠️ 重要说明：
    - 使用 session_context 字段存储会话相关信息，而不是直接的 session_id
    - session_context 包含：session_id, login_type, device_info, login_time, last_activity 等
    - 这样设计的目的是在活跃会话迁移到Redis后，审计日志仍能保持完整的会话上下文信息
    """

    __tablename__ = "audit_logs"

    id: Mapped[int] = Fields.bigint_id(doc="审计日志ID")

    # 多租户字段
    tenant_id: Mapped[str] = Fields.tenant()

    # 审计基本信息字段
    user_id: Mapped[Optional[str]] = Fields.user(required=False)
    action: Mapped[str] = Fields.code(max_length=100, doc="操作类型")
    resource_type: Mapped[str] = Fields.code(max_length=100, doc="资源类型")
    resource_id: Mapped[Optional[str]] = mapped_column(String(64), nullable=True, doc="资源ID")

    # 审计详细信息字段
    details: Mapped[Optional[JSONType]] = Fields.json_field(default={}, doc="操作详细信息")
    ip_address: Mapped[Optional[str]] = Fields.ip_address()
    user_agent: Mapped[Optional[str]] = Fields.long_text(doc="用户代理")

    # 会话上下文字段（替代原来的session_id）
    session_context: Mapped[Optional[JSONType]] = Fields.json_field(
        default={},
        doc="会话上下文信息，包含session_id、login_type、device_info等",
    )

    # 操作结果字段
    result: Mapped[str] = Fields.code(max_length=20, doc="操作结果: success, failure, error")
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True, doc="错误信息")

    # 时间字段（只有created_at，审计日志不允许修改和删除）
    created_at: Mapped[datetime] = Fields.created_at()

    __table_args__ = (
        Index("idx_audit_logs_tenant_user", "tenant_id", "user_id"),
        Index("idx_audit_logs_action", "action"),
        Index("idx_audit_logs_resource", "resource_type", "resource_id"),
        Index("idx_audit_logs_result", "result"),
        Index("idx_audit_logs_created_at", "created_at"),
        Index("idx_audit_logs_tenant_created", "tenant_id", "created_at"),
        Index("idx_audit_logs_user_created", "user_id", "created_at"),
    )

    def set_session_context(self, session_data: Dict[str, Any]) -> None:
        """设置会话上下文信息

        Args:
            session_data: 会话数据，通常包含：
                - session_id: 会话ID
                - login_type: 登录类型
                - device_info: 设备信息
                - login_time: 登录时间
                - last_activity: 最后活动时间
                - ip_address: IP地址
                - user_agent: 用户代理
        """
        if self.session_context is None:
            self.session_context = {}

        # 更新会话上下文
        if isinstance(self.session_context, dict):
            self.session_context.update(session_data)
        else:
            self.session_context = session_data

    def get_session_id(self) -> Optional[str]:
        """获取会话ID"""
        if self.session_context and isinstance(self.session_context, dict):
            return self.session_context.get("session_id")
        return None

    def get_login_type(self) -> Optional[str]:
        """获取登录类型"""
        if self.session_context and isinstance(self.session_context, dict):
            return self.session_context.get("login_type")
        return None

    def get_device_info(self) -> Optional[Dict[str, Any]]:
        """获取设备信息"""
        if self.session_context and isinstance(self.session_context, dict):
            return self.session_context.get("device_info")
        return None

    def __repr__(self) -> str:
        return (f"<AuditLog(id={self.id}, action={self.action}, "
                f"resource_type={self.resource_type}, tenant_id={self.tenant_id})>")


class AuditLogBuilder:
    """审计日志构建器

    提供便捷的方法来创建审计日志，自动设置会话上下文
    """

    @staticmethod
    def create_audit_log(
        tenant_id: str,
        action: str,
        resource_type: str,
        result: str,
        user_id: Optional[str] = None,
        resource_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        session_data: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        error_message: Optional[str] = None,
    ) -> AuditLog:
        """创建审计日志

        Args:
            tenant_id: 租户ID
            action: 操作类型
            resource_type: 资源类型
            result: 操作结果
            user_id: 用户ID（可选）
            resource_id: 资源ID（可选）
            details: 操作详情（可选）
            session_data: 会话数据（可选）
            ip_address: IP地址（可选）
            user_agent: 用户代理（可选）
            error_message: 错误信息（可选）

        Returns:
            AuditLog: 审计日志实例
        """
        audit_log = AuditLog(
            tenant_id=tenant_id,
            user_id=user_id,
            action=action,
            resource_type=resource_type,
            resource_id=resource_id,
            details=details or {},
            ip_address=ip_address,
            user_agent=user_agent,
            result=result,
            error_message=error_message,
        )

        # 设置会话上下文
        if session_data:
            audit_log.set_session_context(session_data)

        return audit_log
