"""
任务调度器

管理 IAM 服务的异步任务和定时任务
"""

from typing import List

from .cleanup_tasks import CleanupTasks
from .notification_tasks import NotificationTasks
from .user_tasks import UserTasks


def dispatch_scheduler() -> List[str]:
    """
    分发任务调度器模块

    Returns:
        需要注入依赖的模块列表
    """
    return ["tasks.user_tasks", "tasks.cleanup_tasks", "tasks.notification_tasks"]


# 任务类实例
user_tasks = UserTasks()
cleanup_tasks = CleanupTasks()
notification_tasks = NotificationTasks()

__all__ = ["dispatch_scheduler", "user_tasks", "cleanup_tasks", "notification_tasks"]
