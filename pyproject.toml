[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "thingsmore"
version = "0.1.0"
description = "ThingsMore Application"
readme = "README.md"
requires-python = ">=3.12"
authors = [
  { name = "Your Name", email = "<EMAIL>" }
]
classifiers = [
  "Development Status :: 3 - Alpha",
  "Intended Audience :: Developers",
  "Programming Language :: Python :: 3.12",
  "Typing :: Typed"
]
dependencies = []

[tool.setuptools]
include-package-data = true

[tool.setuptools.packages.find]
include = ["..commonlib*", "..domain_common*", "..services*"]
exclude = ["tests*", "docs*"]

[tool.setuptools.package-data]
"api.thingsmore" = ["py.typed"]

# Linting 配置
[tool.pylint.format]
max-line-length = 100  # 设置 pylint 行长度限制为100

[tool.flake8]
max-line-length = 100  # flake8 行长度限制
extend-ignore = "B008"  # 忽略 B008 警告
exclude = [
    "migrations",
    "venv",
    ".venv",
    "node_modules"
]

[tool.black]
line-length = 100  # black 格式化行长度
target-version = ["py312"]
skip-string-normalization = true

[tool.isort]
profile = "black"
line_length = 100
known_first_party = ["thingsmore"]