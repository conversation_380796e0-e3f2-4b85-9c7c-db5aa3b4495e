from faststream.redis import RedisBroker, BinaryMessageFormatV1
import asyncio

from commonlib.core.app_config import setup_app_config
from commonlib.core.app_connections import setup_connection_manager
from container import ServiceContainer

config = setup_app_config()
services = ServiceContainer()

# 初始化任务调度器

# 依赖注入模块绑定
services.wire(
    modules=[
        "routes.tenants",
        "routes.users",
        "routes.auth",
        "routes.rbac",
        "routes.roles",
        "routes.permissions",
        "routes.audit",
        "routes.advanced_security",
        "routes.system",
        "routes.system_config",
    ]
)

async def run_consumer(consumer_id: int):
    """单个消费者实例"""
    broker = RedisBroker(
        "redis://:thismore%40123456@192.168.225.14:6378/1",
    message_format=BinaryMessageFormatV1  # 使用新的消息格式
    )

    @broker.subscriber("user_events")
    async def handle(msg: dict):
        print(f"Consumer-{consumer_id} 处理消息: {msg}")
        svc = await ServiceContainer.user_service()
        await svc.process(msg)

    await broker.start()
    return broker  # 返回broker用于关闭


async def run_worker():
    # 初始化容器（所有消费者共享）
    await setup_connection_manager(config)
    await services.init_resources()

    # 启动多个消费者（单进程内并发）
    consumers = [await run_consumer(i) for i in range(3)]  # 启动3个消费者

    try:
        await asyncio.Event().wait()  # 永久运行
    finally:
        for broker in consumers:
            await broker.stop()
        await services.shutdown_resources()


if __name__ == "__main__":
    asyncio.run(run_worker())
