from faststream.redis import RedisBroker, BinaryMessageFormatV1
import asyncio

from bootstrap import setup_bootstrap, runtime
from commonlib.core.tsif_logging import app_logger
from container import ServiceContainer
from services.user_service import UserService
from dependency_injector.wiring import Provide

# 全局配置与服务容器
config, services = setup_bootstrap(role="worker")


async def run_consumer(consumer_id: int):
    """单个消费者实例"""
    # 从配置获取消息代理设置
    broker_config = config.message_broker_config

    broker = RedisBroker(
        broker_config.redis_url,
        message_format=BinaryMessageFormatV1  # 使用新的消息格式
    )

    @broker.subscriber(broker_config.user_events_topic)
    async def handle(
        msg: dict,
        user_service: UserService = Provide[ServiceContainer.user_service]
    ):
        print(f"Consumer-{consumer_id} 处理消息: {msg}")
        await user_service.process(msg)

    await broker.start()
    return broker  # 返回broker用于关闭


async def run_worker():
    """运行Worker进程"""
    async with runtime(config, services, role="worker"):
        # 从配置获取消费者数量
        worker_config = config.worker_config
        concurrency = worker_config.concurrency

        # 启动多个消费者（单进程内并发）
        consumers = [await run_consumer(i) for i in range(concurrency)]

        try:
            await asyncio.Event().wait()  # 永久运行
        finally:
            for broker in consumers:
                await broker.stop()
                app_logger.info(f"broker:{broker} stop.")


if __name__ == "__main__":
    asyncio.run(run_worker())
