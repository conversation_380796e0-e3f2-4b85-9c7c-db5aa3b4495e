"""
安全工具类

提供密码加密、设备指纹、IP检查等安全相关的工具函数
"""

import base64
import hashlib
import io
import re
import secrets
import string
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

import bcrypt
import pyotp
import qrcode


@dataclass
class PasswordPolicy:
    """密码策略"""

    min_length: int = 8
    max_length: int = 128
    require_uppercase: bool = True
    require_lowercase: bool = True
    require_digits: bool = True
    require_special_chars: bool = True
    special_chars: str = "!@#$%^&*()_+-=[]{}|;:,.<>?"
    max_repeated_chars: int = 3
    prevent_common_passwords: bool = True


@dataclass
class DeviceFingerprint:
    """设备指纹"""

    user_agent: str
    screen_resolution: Optional[str] = None
    timezone: Optional[str] = None
    language: Optional[str] = None
    platform: Optional[str] = None
    plugins: Optional[List[str]] = None
    canvas_fingerprint: Optional[str] = None


class SecurityUtils:
    """安全工具类"""

    # 常见弱密码列表（实际应用中应该从文件或数据库加载）
    COMMON_PASSWORDS = {
        "123456",
        "password",
        "123456789",
        "12345678",
        "12345",
        "1234567",
        "1234567890",
        "qwerty",
        "abc123",
        "111111",
        "password123",
        "admin",
        "letmein",
        "welcome",
        "monkey",
    }

    @staticmethod
    def hash_password(password: str) -> str:
        """哈希密码"""
        # TODO: 实现密码哈希逻辑
        # 1. 生成随机盐值
        # 2. 使用bcrypt哈希密码
        # 3. 返回哈希结果

        # 生成盐值并哈希密码
        salt = bcrypt.gensalt(rounds=12)
        hashed = bcrypt.hashpw(password.encode("utf-8"), salt)
        return hashed.decode("utf-8")

    @staticmethod
    def verify_password(password: str, hashed_password: str) -> bool:
        """验证密码"""
        # TODO: 实现密码验证逻辑
        # 1. 使用bcrypt验证密码
        # 2. 处理异常情况
        # 3. 返回验证结果

        try:
            return bcrypt.checkpw(password.encode("utf-8"), hashed_password.encode("utf-8"))
        except Exception:
            return False

    @staticmethod
    def validate_password_strength(
        password: str, policy: Optional[PasswordPolicy] = None
    ) -> Dict[str, Any]:
        """验证密码强度"""
        # TODO: 实现密码强度验证逻辑
        # 1. 检查密码长度
        # 2. 检查字符类型要求
        # 3. 检查重复字符
        # 4. 检查常见密码
        # 5. 返回验证结果

        if policy is None:
            policy = PasswordPolicy()

        result = {
            "is_valid": True,
            "errors": [],
            "strength_score": 0,
            "strength_level": "weak",
        }

        # 检查长度
        if len(password) < policy.min_length:
            result["errors"].append(f"密码长度至少需要{policy.min_length}个字符")
            result["is_valid"] = False
        elif len(password) > policy.max_length:
            result["errors"].append(f"密码长度不能超过{policy.max_length}个字符")
            result["is_valid"] = False
        else:
            result["strength_score"] += 20

        # 检查大写字母
        if policy.require_uppercase and not re.search(r"[A-Z]", password):
            result["errors"].append("密码必须包含大写字母")
            result["is_valid"] = False
        elif re.search(r"[A-Z]", password):
            result["strength_score"] += 15

        # 检查小写字母
        if policy.require_lowercase and not re.search(r"[a-z]", password):
            result["errors"].append("密码必须包含小写字母")
            result["is_valid"] = False
        elif re.search(r"[a-z]", password):
            result["strength_score"] += 15

        # 检查数字
        if policy.require_digits and not re.search(r"\d", password):
            result["errors"].append("密码必须包含数字")
            result["is_valid"] = False
        elif re.search(r"\d", password):
            result["strength_score"] += 15

        # 检查特殊字符
        if policy.require_special_chars:
            special_pattern = f"[{re.escape(policy.special_chars)}]"
            if not re.search(special_pattern, password):
                result["errors"].append("密码必须包含特殊字符")
                result["is_valid"] = False
            elif re.search(special_pattern, password):
                result["strength_score"] += 20

        # 检查重复字符
        if SecurityUtils._has_repeated_chars(password, policy.max_repeated_chars):
            result["errors"].append(f"密码不能包含超过{policy.max_repeated_chars}个连续相同字符")
            result["is_valid"] = False
        else:
            result["strength_score"] += 10

        # 检查常见密码
        if policy.prevent_common_passwords and password.lower() in SecurityUtils.COMMON_PASSWORDS:
            result["errors"].append("不能使用常见密码")
            result["is_valid"] = False
        else:
            result["strength_score"] += 5

        # 计算强度等级
        if result["strength_score"] >= 80:
            result["strength_level"] = "strong"
        elif result["strength_score"] >= 60:
            result["strength_level"] = "medium"
        else:
            result["strength_level"] = "weak"

        return result

    @staticmethod
    def _has_repeated_chars(password: str, max_repeated: int) -> bool:
        """检查是否有过多重复字符"""
        count = 1
        for i in range(1, len(password)):
            if password[i] == password[i - 1]:
                count += 1
                if count > max_repeated:
                    return True
            else:
                count = 1
        return False

    @staticmethod
    def generate_secure_token(length: int = 32) -> str:
        """生成安全令牌"""
        # TODO: 实现安全令牌生成
        # 1. 使用密码学安全的随机数生成器
        # 2. 生成指定长度的令牌
        # 3. 返回十六进制字符串

        return secrets.token_hex(length)

    @staticmethod
    def generate_device_fingerprint(device_info: DeviceFingerprint) -> str:
        """生成设备指纹"""
        # TODO: 实现设备指纹生成逻辑
        # 1. 收集设备信息
        # 2. 标准化处理
        # 3. 计算哈希值
        # 4. 返回设备指纹

        # 构建指纹字符串
        fingerprint_data = [
            device_info.user_agent or "",
            device_info.screen_resolution or "",
            device_info.timezone or "",
            device_info.language or "",
            device_info.platform or "",
            "|".join(device_info.plugins or []),
            device_info.canvas_fingerprint or "",
        ]

        fingerprint_str = "|".join(fingerprint_data)
        return hashlib.sha256(fingerprint_str.encode()).hexdigest()

    @staticmethod
    def is_valid_ip_address(ip: str) -> bool:
        """验证IP地址格式"""
        # TODO: 实现IP地址验证
        # 1. 检查IPv4格式
        # 2. 检查IPv6格式
        # 3. 返回验证结果

        # 简单的IPv4验证
        ipv4_pattern = r"^(\d{1,3}\.){3}\d{1,3}$"
        if re.match(ipv4_pattern, ip):
            parts = ip.split(".")
            return all(0 <= int(part) <= 255 for part in parts)

        # 这里可以添加IPv6验证
        return False

    @staticmethod
    def get_ip_location(ip: str) -> Optional[Dict[str, str]]:
        """获取IP地理位置"""
        # TODO: 实现IP地理位置查询
        # 1. 调用地理位置API
        # 2. 解析响应数据
        # 3. 返回位置信息
        # 4. 处理异常情况

        # 这里需要集成第三方地理位置服务
        # 如 MaxMind GeoIP2, IP2Location 等
        return {
            "country": "Unknown",
            "region": "Unknown",
            "city": "Unknown",
            "latitude": "0",
            "longitude": "0",
        }

    @staticmethod
    def calculate_distance_km(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """计算两个地理位置之间的距离（公里）"""
        # TODO: 实现地理距离计算
        # 1. 使用Haversine公式
        # 2. 计算球面距离
        # 3. 返回公里数

        import math

        # 地球半径（公里）
        R = 6371.0

        # 转换为弧度
        lat1_rad = math.radians(lat1)
        lon1_rad = math.radians(lon1)
        lat2_rad = math.radians(lat2)
        lon2_rad = math.radians(lon2)

        # 计算差值
        dlat = lat2_rad - lat1_rad
        dlon = lon2_rad - lon1_rad

        # Haversine公式
        a = (
            math.sin(dlat / 2) ** 2
            + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon / 2) ** 2
        )
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))

        return R * c

    @staticmethod
    def is_suspicious_login(
        current_ip: str,
        last_ip: str,
        current_location: Optional[Dict[str, str]] = None,
        last_location: Optional[Dict[str, str]] = None,
        time_diff_minutes: int = 0,
    ) -> Dict[str, Any]:
        """检测可疑登录"""
        # TODO: 实现可疑登录检测逻辑
        # 1. 检查IP地址变化
        # 2. 检查地理位置变化
        # 3. 检查时间间隔
        # 4. 计算风险评分
        # 5. 返回检测结果

        result = {"is_suspicious": False, "risk_score": 0, "reasons": []}

        # IP地址完全不同
        if current_ip != last_ip:
            result["risk_score"] += 30
            result["reasons"].append("IP地址变化")

        # 地理位置检查
        if current_location and last_location:
            try:
                current_lat = float(current_location.get("latitude", 0))
                current_lon = float(current_location.get("longitude", 0))
                last_lat = float(last_location.get("latitude", 0))
                last_lon = float(last_location.get("longitude", 0))

                distance = SecurityUtils.calculate_distance_km(
                    current_lat, current_lon, last_lat, last_lon
                )

                # 距离超过100公里且时间间隔小于1小时
                if distance > 100 and time_diff_minutes < 60:
                    result["risk_score"] += 50
                    result["reasons"].append(f"地理位置异常变化（{distance:.1f}公里）")
                elif distance > 1000:
                    result["risk_score"] += 30
                    result["reasons"].append("跨国登录")

            except (ValueError, TypeError):
                pass

        # 时间间隔异常
        if time_diff_minutes < 5:
            result["risk_score"] += 20
            result["reasons"].append("登录间隔过短")

        # 判断是否可疑
        result["is_suspicious"] = result["risk_score"] >= 50

        return result

    @staticmethod
    def sanitize_user_input(input_str: str) -> str:
        """清理用户输入"""
        # TODO: 实现用户输入清理
        # 1. 移除危险字符
        # 2. 转义特殊字符
        # 3. 限制长度
        # 4. 返回清理后的字符串

        if not input_str:
            return ""

        # 移除控制字符
        sanitized = re.sub(r"[\x00-\x1f\x7f-\x9f]", "", input_str)

        # 限制长度
        sanitized = sanitized[:1000]

        # 移除首尾空白
        sanitized = sanitized.strip()

        return sanitized

    @staticmethod
    def generate_csrf_token() -> str:
        """生成CSRF令牌"""
        return SecurityUtils.generate_secure_token(16)

    @staticmethod
    def verify_csrf_token(token: str, expected_token: str) -> bool:
        """验证CSRF令牌"""
        return secrets.compare_digest(token, expected_token)

    @staticmethod
    def rate_limit_key(identifier: str, action: str, window_minutes: int = 60) -> str:
        """生成限流键"""
        # TODO: 实现限流键生成
        # 1. 构建限流标识
        # 2. 包含时间窗口
        # 3. 返回Redis键

        timestamp = int(datetime.now().timestamp() // (window_minutes * 60))
        return f"rate_limit:{action}:{identifier}:{timestamp}"

    # ===== TOTP相关功能 =====

    @staticmethod
    def generate_totp_secret() -> str:
        """
        生成TOTP密钥

        Returns:
            Base32编码的密钥
        """
        return pyotp.random_base32()

    @staticmethod
    def generate_totp_qr_url(secret: str, user_email: str, issuer: str) -> str:
        """
        生成TOTP二维码URL

        Args:
            secret: TOTP密钥
            user_email: 用户邮箱
            issuer: 发行者名称

        Returns:
            二维码URL（data:image/png;base64格式）
        """
        totp = pyotp.TOTP(secret)
        provisioning_uri = totp.provisioning_uri(name=user_email, issuer_name=issuer)

        # 生成二维码
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(provisioning_uri)
        qr.make(fit=True)

        img = qr.make_image(fill_color="black", back_color="white")

        # 转换为base64
        img_buffer = io.BytesIO()
        img.save(img_buffer, format="PNG")
        img_str = base64.b64encode(img_buffer.getvalue()).decode()

        return f"data:image/png;base64,{img_str}"

    @staticmethod
    def generate_totp_code(secret: str) -> str:
        """
        生成TOTP验证码

        Args:
            secret: TOTP密钥

        Returns:
            6位验证码
        """
        totp = pyotp.TOTP(secret)
        return totp.now()

    @staticmethod
    def verify_totp(secret: str, token: str, window: int = 1) -> bool:
        """
        验证TOTP验证码

        Args:
            secret: TOTP密钥
            token: 验证码
            window: 时间窗口（允许前后几个时间段）

        Returns:
            验证结果
        """
        totp = pyotp.TOTP(secret)
        return totp.verify(token, valid_window=window)

    # ===== 备用恢复码 =====

    @staticmethod
    def generate_backup_code(length: int = 8) -> str:
        """
        生成备用恢复码

        Args:
            length: 恢复码长度

        Returns:
            恢复码
        """
        chars = string.ascii_uppercase + string.digits
        return "".join(secrets.choice(chars) for _ in range(length))

    @staticmethod
    def generate_backup_codes(count: int = 10, length: int = 8) -> List[str]:
        """
        生成多个备用恢复码

        Args:
            count: 生成数量
            length: 每个恢复码长度

        Returns:
            恢复码列表
        """
        return [SecurityUtils.generate_backup_code(length) for _ in range(count)]

    # ===== 密码生成 =====

    @staticmethod
    def generate_password(length: int = 12, include_symbols: bool = True) -> str:
        """
        生成随机密码

        Args:
            length: 密码长度
            include_symbols: 是否包含特殊字符

        Returns:
            生成的密码
        """
        chars = string.ascii_letters + string.digits
        if include_symbols:
            chars += "!@#$%^&*"

        password = "".join(secrets.choice(chars) for _ in range(length))

        # 确保密码包含大小写字母和数字
        if not any(c.islower() for c in password):
            password = password[:-1] + secrets.choice(string.ascii_lowercase)
        if not any(c.isupper() for c in password):
            password = password[:-1] + secrets.choice(string.ascii_uppercase)
        if not any(c.isdigit() for c in password):
            password = password[:-1] + secrets.choice(string.digits)

        return password

    # ===== API密钥生成 =====

    @staticmethod
    def generate_api_key(prefix: str = "iam") -> str:
        """
        生成API密钥

        Args:
            prefix: 前缀

        Returns:
            API密钥
        """
        return f"{prefix}_{secrets.token_urlsafe(32)}"

    # ===== 时间相关工具 =====

    @staticmethod
    def is_token_expired(created_at: datetime, ttl_seconds: int) -> bool:
        """
        检查令牌是否过期

        Args:
            created_at: 创建时间
            ttl_seconds: 生存时间（秒）

        Returns:
            是否过期
        """
        expiry_time = created_at + timedelta(seconds=ttl_seconds)
        return datetime.utcnow() > expiry_time

    @staticmethod
    def get_token_remaining_time(created_at: datetime, ttl_seconds: int) -> int:
        """
        获取令牌剩余时间

        Args:
            created_at: 创建时间
            ttl_seconds: 生存时间（秒）

        Returns:
            剩余时间（秒）
        """
        expiry_time = created_at + timedelta(seconds=ttl_seconds)
        remaining = expiry_time - datetime.utcnow()
        return max(0, int(remaining.total_seconds()))

    # ===== URL安全检查 =====

    @staticmethod
    def is_safe_url(url: str, allowed_hosts: List[str]) -> bool:
        """
        检查URL是否安全

        Args:
            url: 要检查的URL
            allowed_hosts: 允许的主机列表

        Returns:
            是否安全
        """
        if not url:
            return False

        # 检查是否为相对URL
        if url.startswith("/"):
            return True

        # 检查是否在允许的主机列表中
        from urllib.parse import urlparse

        try:
            parsed = urlparse(url)
            return parsed.netloc in allowed_hosts
        except Exception:
            return False


if __name__ == "__main__":
    print(SecurityUtils.hash_password("123456"))
