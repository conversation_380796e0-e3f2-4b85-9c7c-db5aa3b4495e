import json
import logging
import sys
from pathlib import Path
from typing import Dict, Optional, Union

from loguru import logger
from orjson import orjson

from commonlib.core.context import recyclable_app_correlation_id
from commonlib.core.context.context import RecyclableContextVar
from commonlib.utils.singleton import SingletonMeta

logger.level("BIZ", no=25, color="<cyan>")
logger.level("JSON4BIZ", no=26, color="<blue>")


# 预定义的日志级别
class LogLevel:
    INFO = "INFO"
    ERROR = "ERROR"
    EXCEPTION = "EXCEPTION"
    DEBUG = "DEBUG"
    WARNING = "WARNING"
    BIZ = "BIZ"
    JSON4BIZ = "JSON4BIZ"


class LogConfig:
    """日志配置类"""

    def __init__(
        self,
        *,
        app_name: str,
        log_dir: Union[str, Path],
        debug: bool = False,
        rotation: str = "00:00",
        retention: str = "7 days",
        log_level: str = LogLevel.INFO,
        json_format: bool = False,
        compression: str = "gz",  # 新增压缩参数
    ):
        self.app_name = app_name
        self.log_dir = Path(log_dir)
        self.debug = debug
        self.rotation = rotation
        self.retention = retention
        self.log_level = log_level
        self.json_format = json_format
        self.compression = compression
        # 确保日志目录存在
        self.log_dir.mkdir(parents=True, exist_ok=True, mode=0o750)
        self._validate_levels()

    @staticmethod
    def _validate_levels():
        """验证自定义级别不冲突"""
        assert logger.level(LogLevel.BIZ).no == 25, "BIZ级别数值冲突"
        assert logger.level(LogLevel.JSON4BIZ).no == 26, "JSON4BIZ级别数值冲突"

    def log_filter(self, level: str):
        def inner_filter(record: dict):
            correlation_id = recyclable_app_correlation_id.get() or "appserver"
            record["extra"].setdefault("correlation_id", correlation_id)
            record["extra"].setdefault("app_name", self.app_name)
            return record["level"].name == level

        return inner_filter

    def get_handlers(self) -> list[Dict]:
        """获取日志处理器配置"""
        handlers = []

        # 基础处理器配置
        file_base_config = {
            "enqueue": True,
            "rotation": self.rotation,
            "retention": self.retention,
            "encoding": "utf-8",
            "compression": self.compression,
            "mode": "a",
            "catch": True,  # 防止处理器崩溃
        }

        # 日志格式定义
        logger_fmt = (
            "[{time:YYYY-MM-DD HH:mm:ss.SSS}] "
            "[{level}] "
            "[{extra[correlation_id]}] - "
            "[{extra[app_name]}] {message}"
        )
        logger_error_fmt = (
            "[{time:YYYY-MM-DD HH:mm:ss.SSS}] "
            "[{level}] "
            "[{extra[correlation_id]}] - "
            "[{extra[app_name]}] {message}\n{exception}"
        )

        # 定义标准日志级别配置
        standard_log_levels = [
            {
                "level": LogLevel.BIZ,
                "format": logger_fmt,
                "path_template": "{level}/{level_lower}_{app_name}.{{time:YYYY-MM-DD}}.log",
                "extra_config": {},
            },
            {
                "level": LogLevel.INFO,
                "format": logger_fmt,
                "path_template": "{level}/{level_lower}_{app_name}.{{time:YYYY-MM-DD}}.log",
                "extra_config": {},
            },
            {
                "level": LogLevel.ERROR,
                "format": logger_error_fmt,
                "path_template": "{level}/{level_lower}_{app_name}.{{time:YYYY-MM-DD}}.log",
                "extra_config": {
                    "backtrace": True,  # 显示完整调用链
                    "diagnose": True,  # 显示变量值
                },
            },
            {
                "level": LogLevel.JSON4BIZ,
                "format": logger_error_fmt,
                "path_template": "JSON4BIZ/json4biz_{app_name}.{{time:YYYY-MM-DD}}.log",
                "extra_config": {},
            },
        ]

        # 创建所有处理器
        app_name_lower = self.app_name.lower()
        for config in standard_log_levels:
            level = config["level"]
            level_lower = level.lower()

            # 构建日志文件路径
            path = config["path_template"].format(
                level=level, level_lower=level_lower, app_name=app_name_lower
            )

            # 创建处理器配置
            handler_config = {
                **file_base_config,
                "level": level,
                "format": config["format"],
                "filter": self.log_filter(level),
                "sink": str(self.log_dir / path),
                **config["extra_config"],
            }

            handlers.append(handler_config)

        # 调试模式下添加控制台输出
        if self.debug:
            logger.add(sys.stderr, level="DEBUG")

        return handlers


class AppLogger(metaclass=SingletonMeta):
    """全局日志器，提供统一的日志记录接口"""

    # 敏感字段集合（会在日志中被过滤）
    SENSITIVE_KEYS = {"password", "credit_card", "auth_token", "token", "secret"}

    def __init__(self):
        self._logger: Optional[logger] = None
        self._app_name: str = "unknown"
        self._log_dir: str = ""
        self._debug: bool = False
        self._json_format: bool = False
        self._log_config: Optional[LogConfig] = None
        self.is_init: bool = False

    def initialize(
        self,
        app_name: str = "unknown",
        log_dir: Union[str, Path] = "logs",
        debug: bool = False,
        json_format: bool = False,
        log_modules: Optional[Dict[str, int]] = None,
    ):
        """初始化日志系统

        Args:
            app_name: 应用名称，用于日志标识
            log_dir: 日志文件存储目录
            debug: 是否启用调试模式（会输出更详细的日志）
            json_format: 是否使用JSON格式输出
            log_modules: 需要特殊处理的模块日志级别配置，例如：
                {"celery": logging.DEBUG if debug else logging.INFO}
        """
        # 基础配置初始化
        self.is_init = True
        self._app_name = app_name
        self._log_dir = log_dir
        self._debug = debug
        self._json_format = json_format

        # 创建日志配置并设置处理器
        self._log_config = LogConfig(
            app_name=app_name, log_dir=log_dir, debug=debug, json_format=json_format
        )

        # 清除现有处理器并添加新处理器
        logger.remove()
        for handler in self._log_config.get_handlers():
            logger.add(**handler)

        # 处理特定模块的日志
        if log_modules:

            class SelectiveInterceptHandler(logging.Handler):
                def emit(self, record):
                    # 只处理配置过的模块日志
                    if record.name in log_modules:
                        level = logger.level(record.levelname).name
                        logger.opt(depth=6, exception=record.exc_info).bind(
                            module=record.name, lineno=record.lineno
                        ).log(level, record.getMessage())

            # 为每个指定的模块设置拦截器
            for module_name in log_modules:
                logging.getLogger(module_name).handlers = [SelectiveInterceptHandler()]
                logging.getLogger(module_name).propagate = False  # 阻止传播到根日志器

        # 设置日志器和上下文
        self._logger = logger
        RecyclableContextVar.increment_thread_recycles()
        recyclable_app_correlation_id.set("system-init")

    def _sanitize_message(self, message: Union[str, dict]) -> str:
        """对日志消息进行脱敏处理，移除敏感信息

        Args:
            message: 原始日志消息（字符串或字典）

        Returns:
            处理后的安全日志消息
        """
        if isinstance(message, dict):
            # 深度脱敏处理
            def redact(obj):
                if isinstance(obj, dict):
                    return {
                        k: "***" if k.lower() in self.SENSITIVE_KEYS else redact(v)
                        for k, v in obj.items()
                    }
                elif isinstance(obj, list):
                    return [redact(item) for item in obj]
                return obj

            # 使用orjson提高性能
            try:
                return orjson.dumps(redact(message)).decode("utf-8")
            except (TypeError, ValueError):
                # 回退到标准json
                return json.dumps(redact(message), default=str)

        # 处理字符串中的花括号，避免格式化问题
        return str(message).translate(str.maketrans({"{": "{{", "}": "}}"}))

    def _log_with_depth(
        self,
        level: str,
        message: Union[str, dict],
        *,
        exception: bool = False,
        record: bool = True,
        colors: bool = True,
        depth: int = 2,
        **extra,
    ):
        """增强的日志记录方法（兼容异常和结构化日志）

        Args:
            level: 日志级别 (e.g. "ERROR")
            message: 日志消息（字符串或字典）
            exception: 是否包含异常信息
            record: 是否记录调用位置
            colors: 是否启用颜色
            depth: 调用栈深度
            **extra: 额外日志字段
        """
        if not self.is_init:
            # 确保日志系统已初始化
            print(f"WARNING: Logger not initialized. Message: {message}")
            return

        # 消息标准化处理
        sanitized_msg = self._sanitize_message(message)

        # 创建优化后的日志选项
        log_opt = self._logger.opt(
            depth=depth,
            record=record,
            colors=colors,
            lazy=extra.pop("lazy", False),
        )

        # 异常处理逻辑
        if exception:
            log_opt = log_opt.opt(exception=exception)

        # 结构化日志输出
        log_opt.log(
            level,
            "{}",
            sanitized_msg,
            **self._sanitize_extra(extra),
            diagnose=False,  # 过滤敏感字段
        )

    def _sanitize_extra(self, extra: dict) -> dict:
        """过滤日志额外字段中的敏感信息

        Args:
            extra: 额外字段字典

        Returns:
            过滤后的安全字典
        """
        return {
            k: "[FILTERED]" if k.lower() in self.SENSITIVE_KEYS else v for k, v in extra.items()
        }

    def debug(self, message: Union[str, dict], **kwargs) -> None:
        """记录调试级别日志

        Args:
            message: 日志消息（字符串或字典）
            **kwargs: 额外的日志字段
        """
        self._log_with_depth(LogLevel.DEBUG, message, **kwargs)

    def info(self, message: Union[str, dict], **kwargs) -> None:
        """记录信息级别日志

        Args:
            message: 日志消息（字符串或字典）
            **kwargs: 额外的日志字段
        """
        self._log_with_depth(LogLevel.INFO, message, **kwargs)

    def warning(self, message: Union[str, dict], **kwargs) -> None:
        """记录警告级别日志

        Args:
            message: 日志消息（字符串或字典）
            **kwargs: 额外的日志字段
        """
        self._log_with_depth(LogLevel.WARNING, message, **kwargs)

    def error(self, message: Union[str, dict], exception: bool = False, **kwargs) -> None:
        """记录错误级别日志

        Args:
            message: 日志消息（字符串或字典）
            exception: 是否包含异常堆栈信息
            **kwargs: 额外的日志字段
        """
        self._log_with_depth(LogLevel.ERROR, message, exception=exception, **kwargs)

    def exception(self, message: Union[str, dict], exception: bool = True, **kwargs) -> None:
        """记录异常日志（包含堆栈信息）

        Args:
            message: 日志消息（字符串或字典）
            exception: 是否包含异常堆栈信息，默认为True
            **kwargs: 额外的日志字段
        """
        self._log_with_depth(LogLevel.ERROR, message, exception=exception, **kwargs)

    def biz(self, message: Union[str, Dict], **kwargs) -> None:
        """记录业务级别日志

        Args:
            message: 日志消息（字符串或字典）
            **kwargs: 额外的日志字段
        """
        self._log_with_depth(LogLevel.BIZ, message, **kwargs)

    def json4biz(self, message: Union[str, Dict], **kwargs) -> None:
        """记录JSON格式的业务日志

        Args:
            message: 日志消息（字符串或字典）
            **kwargs: 额外的日志字段
        """
        self._log_with_depth(LogLevel.JSON4BIZ, message, **kwargs)


app_logger = AppLogger()

if __name__ == "__main__":
    app_logger.initialize(debug=True)
    app_logger.info("hello world")
