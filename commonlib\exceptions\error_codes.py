from enum import Enum, unique
from typing import Dict, Optional

from pydantic import BaseModel, Field


@unique
class ErrorDomain(str, Enum):
    """错误领域分类"""

    VALIDATION = "validation"  # 数据验证错误 (400)
    AUTHENTICATION = "auth"  # 认证错误 (401)
    AUTHORIZATION = "permission"  # 授权错误 (403)
    NOT_FOUND = "not_found"  # 资源不存在 (404)
    BUSINESS = "business"  # 业务逻辑错误 (4xx)
    CONFLICT = "conflict"  # 资源冲突 (409)
    RATE_LIMIT = "rate_limit"  # 请求限流 (429)
    EXTERNAL = "external"  # 外部服务错误 (5xx)
    INTERNAL = "internal"  # 内部服务错误 (5xx)
    INTEGRATION = "integration"  # 集成错误 (5xx)
    DATABASE = "database"  # 数据库错误 (5xx)


@unique
class ErrorLevel(str, Enum):
    """错误严重等级"""

    INFO = "info"  # 提示信息
    WARNING = "warning"  # 可恢复错误
    ERROR = "error"  # 需人工干预
    CRITICAL = "critical"  # 系统不可用


class ErrorMeta(BaseModel):
    """错误元数据"""

    http_status: int = Field(..., description="关联的HTTP状态码")
    level: ErrorLevel = Field(ErrorLevel.ERROR, description="错误级别")
    i18n_key: Optional[str] = Field(None, description="国际化文案key")


class ErrorCode:
    """错误代码对象"""

    __slots__ = ("code", "type", "meta", "_domain")

    def __init__(self, domain: ErrorDomain, code: int, meta: ErrorMeta):
        """
        :param domain: 错误领域
        :param code: 具体错误码 (范围1000-9999)
        :param meta: 错误元数据
        """
        if not 1000 <= code <= 9999:
            raise ValueError("Error code must be between 1000-9999")

        self._domain = domain
        self.code = int(f"{self._domain_code}{code}")
        self.type = f"{domain.value}_{code}"
        self.meta = meta

    @property
    def _domain_code(self) -> int:
        """各领域的基础错误代码"""
        mapping = {
            ErrorDomain.VALIDATION: 4000,
            ErrorDomain.AUTHENTICATION: 4010,
            ErrorDomain.AUTHORIZATION: 4030,
            ErrorDomain.NOT_FOUND: 4040,
            ErrorDomain.BUSINESS: 4001,
            ErrorDomain.CONFLICT: 4090,
            ErrorDomain.RATE_LIMIT: 4290,
            ErrorDomain.EXTERNAL: 5020,
            ErrorDomain.INTERNAL: 5000,
            ErrorDomain.INTEGRATION: 5001,
            ErrorDomain.DATABASE: 5002,
        }
        return mapping[self._domain]

    def to_dict(self) -> Dict:
        """转为API响应字典"""
        return {"code": self.code, "type": self.type, **self.meta.model_dump()}


class ValidationErrors:
    """400类错误 - 数据验证"""

    INVALID_FORMAT = ErrorCode(
        domain=ErrorDomain.VALIDATION,
        code=1001,
        meta=ErrorMeta(
            http_status=400,
            i18n_key="error.validation.invalid_format",
            level=ErrorLevel.WARNING,
        ),
    )

    MISSING_FIELD = ErrorCode(
        domain=ErrorDomain.VALIDATION,
        code=1002,
        meta=ErrorMeta(
            http_status=400,
            i18n_key="error.validation.missing_field",
            level=ErrorLevel.WARNING,
        ),
    )

    INVALID_VALUE = ErrorCode(
        domain=ErrorDomain.VALIDATION,
        code=1003,
        meta=ErrorMeta(
            http_status=400,
            i18n_key="error.validation.invalid_value",
            level=ErrorLevel.WARNING,
        ),
    )

    FIELD_TOO_LONG = ErrorCode(
        domain=ErrorDomain.VALIDATION,
        code=1004,
        meta=ErrorMeta(
            http_status=400,
            i18n_key="error.validation.field_too_long",
            level=ErrorLevel.WARNING,
        ),
    )

    FIELD_TOO_SHORT = ErrorCode(
        domain=ErrorDomain.VALIDATION,
        code=1005,
        meta=ErrorMeta(
            http_status=400,
            i18n_key="error.validation.field_too_short",
            level=ErrorLevel.WARNING,
        ),
    )

    INVALID_EMAIL = ErrorCode(
        domain=ErrorDomain.VALIDATION,
        code=1006,
        meta=ErrorMeta(
            http_status=400,
            i18n_key="error.validation.invalid_email",
            level=ErrorLevel.WARNING,
        ),
    )

    INVALID_PHONE = ErrorCode(
        domain=ErrorDomain.VALIDATION,
        code=1007,
        meta=ErrorMeta(
            http_status=400,
            i18n_key="error.validation.invalid_phone",
            level=ErrorLevel.WARNING,
        ),
    )

    INVALID_DATE = ErrorCode(
        domain=ErrorDomain.VALIDATION,
        code=1008,
        meta=ErrorMeta(
            http_status=400,
            i18n_key="error.validation.invalid_date",
            level=ErrorLevel.WARNING,
        ),
    )

    INVALID_JSON = ErrorCode(
        domain=ErrorDomain.VALIDATION,
        code=1009,
        meta=ErrorMeta(
            http_status=400,
            i18n_key="error.validation.invalid_json",
            level=ErrorLevel.WARNING,
        ),
    )

    SCHEMA_VALIDATION_FAILED = ErrorCode(
        domain=ErrorDomain.VALIDATION,
        code=1010,
        meta=ErrorMeta(
            http_status=400,
            i18n_key="error.validation.schema_failed",
            level=ErrorLevel.WARNING,
        ),
    )


class AuthErrors:
    """401类错误 - 认证"""

    INVALID_CREDENTIALS = ErrorCode(
        domain=ErrorDomain.AUTHENTICATION,
        code=1001,
        meta=ErrorMeta(
            http_status=401,
            i18n_key="error.auth.invalid_credentials",
            level=ErrorLevel.WARNING,
        ),
    )

    EXPIRED_TOKEN = ErrorCode(
        domain=ErrorDomain.AUTHENTICATION,
        code=1002,
        meta=ErrorMeta(
            http_status=401,
            i18n_key="error.auth.expired_token",
            level=ErrorLevel.WARNING,
        ),
    )

    INVALID_TOKEN = ErrorCode(
        domain=ErrorDomain.AUTHENTICATION,
        code=1003,
        meta=ErrorMeta(
            http_status=401,
            i18n_key="error.auth.invalid_token",
            level=ErrorLevel.WARNING,
        ),
    )

    MISSING_TOKEN = ErrorCode(
        domain=ErrorDomain.AUTHENTICATION,
        code=1004,
        meta=ErrorMeta(
            http_status=401,
            i18n_key="error.auth.missing_token",
            level=ErrorLevel.WARNING,
        ),
    )

    TOKEN_REVOKED = ErrorCode(
        domain=ErrorDomain.AUTHENTICATION,
        code=1005,
        meta=ErrorMeta(
            http_status=401,
            i18n_key="error.auth.token_revoked",
            level=ErrorLevel.WARNING,
        ),
    )

    INVALID_REFRESH_TOKEN = ErrorCode(
        domain=ErrorDomain.AUTHENTICATION,
        code=1006,
        meta=ErrorMeta(
            http_status=401,
            i18n_key="error.auth.invalid_refresh_token",
            level=ErrorLevel.WARNING,
        ),
    )

    ACCOUNT_LOCKED = ErrorCode(
        domain=ErrorDomain.AUTHENTICATION,
        code=1007,
        meta=ErrorMeta(
            http_status=401,
            i18n_key="error.auth.account_locked",
            level=ErrorLevel.ERROR,
        ),
    )

    ACCOUNT_DISABLED = ErrorCode(
        domain=ErrorDomain.AUTHENTICATION,
        code=1008,
        meta=ErrorMeta(
            http_status=401,
            i18n_key="error.auth.account_disabled",
            level=ErrorLevel.ERROR,
        ),
    )


class PermissionErrors:
    """403类错误 - 权限"""

    INSUFFICIENT_PERMISSION = ErrorCode(
        domain=ErrorDomain.AUTHORIZATION,
        code=1001,
        meta=ErrorMeta(
            http_status=403,
            i18n_key="error.permission.insufficient",
            level=ErrorLevel.ERROR,
        ),
    )

    ACCESS_DENIED = ErrorCode(
        domain=ErrorDomain.AUTHORIZATION,
        code=1002,
        meta=ErrorMeta(
            http_status=403,
            i18n_key="error.permission.access_denied",
            level=ErrorLevel.ERROR,
        ),
    )

    RESOURCE_FORBIDDEN = ErrorCode(
        domain=ErrorDomain.AUTHORIZATION,
        code=1003,
        meta=ErrorMeta(
            http_status=403,
            i18n_key="error.permission.resource_forbidden",
            level=ErrorLevel.ERROR,
        ),
    )

    OPERATION_NOT_ALLOWED = ErrorCode(
        domain=ErrorDomain.AUTHORIZATION,
        code=1004,
        meta=ErrorMeta(
            http_status=403,
            i18n_key="error.permission.operation_not_allowed",
            level=ErrorLevel.ERROR,
        ),
    )

    ROLE_REQUIRED = ErrorCode(
        domain=ErrorDomain.AUTHORIZATION,
        code=1005,
        meta=ErrorMeta(
            http_status=403,
            i18n_key="error.permission.role_required",
            level=ErrorLevel.ERROR,
        ),
    )

    AUTHENTICATION = ErrorCode(
        domain=ErrorDomain.AUTHORIZATION,
        code=1005,
        meta=ErrorMeta(
            http_status=403,
            i18n_key="error.permission.authentication",
            level=ErrorLevel.ERROR,
        ),
    )
    AUTHORIZATION = ErrorCode(
        domain=ErrorDomain.AUTHORIZATION,
        code=1005,
        meta=ErrorMeta(
            http_status=403,
            i18n_key="error.permission.authorization",
            level=ErrorLevel.ERROR,
        ),
    )


class BusinessErrors:
    """业务逻辑错误"""

    INVALID_OPERATION = ErrorCode(
        domain=ErrorDomain.BUSINESS,
        code=2001,
        meta=ErrorMeta(
            http_status=400,
            i18n_key="error.business.invalid_operation",
            level=ErrorLevel.ERROR,
        ),
    )

    INVALID_STATE = ErrorCode(
        domain=ErrorDomain.BUSINESS,
        code=2002,
        meta=ErrorMeta(
            http_status=400,
            i18n_key="error.business.invalid_state",
            level=ErrorLevel.ERROR,
        ),
    )

    OPERATION_FAILED = ErrorCode(
        domain=ErrorDomain.BUSINESS,
        code=2003,
        meta=ErrorMeta(
            http_status=400,
            i18n_key="error.business.operation_failed",
            level=ErrorLevel.ERROR,
        ),
    )

    RESOURCE_LOCKED = ErrorCode(
        domain=ErrorDomain.BUSINESS,
        code=2004,
        meta=ErrorMeta(
            http_status=423,
            i18n_key="error.business.resource_locked",
            level=ErrorLevel.WARNING,
        ),
    )

    QUOTA_EXCEEDED = ErrorCode(
        domain=ErrorDomain.BUSINESS,
        code=2005,
        meta=ErrorMeta(
            http_status=429,
            i18n_key="error.business.quota_exceeded",
            level=ErrorLevel.WARNING,
        ),
    )


class RateLimitErrors:
    """429类错误 - 请求限流"""

    TOO_MANY_REQUESTS = ErrorCode(
        domain=ErrorDomain.RATE_LIMIT,
        code=1001,
        meta=ErrorMeta(
            http_status=429,
            i18n_key="error.rate_limit.too_many_requests",
            level=ErrorLevel.WARNING,
        ),
    )

    API_QUOTA_EXCEEDED = ErrorCode(
        domain=ErrorDomain.RATE_LIMIT,
        code=1002,
        meta=ErrorMeta(
            http_status=429,
            i18n_key="error.rate_limit.quota_exceeded",
            level=ErrorLevel.WARNING,
        ),
    )

    CONCURRENT_LIMIT_EXCEEDED = ErrorCode(
        domain=ErrorDomain.RATE_LIMIT,
        code=1003,
        meta=ErrorMeta(
            http_status=429,
            i18n_key="error.rate_limit.concurrent_limit",
            level=ErrorLevel.WARNING,
        ),
    )


class ConflictErrors:
    """409类错误 - 资源冲突"""

    RESOURCE_CONFLICT = ErrorCode(
        domain=ErrorDomain.CONFLICT,
        code=1001,
        meta=ErrorMeta(
            http_status=409,
            i18n_key="error.conflict.resource_conflict",
            level=ErrorLevel.WARNING,
        ),
    )

    VERSION_CONFLICT = ErrorCode(
        domain=ErrorDomain.CONFLICT,
        code=1002,
        meta=ErrorMeta(
            http_status=409,
            i18n_key="error.conflict.version_conflict",
            level=ErrorLevel.WARNING,
        ),
    )

    DUPLICATE_RESOURCE = ErrorCode(
        domain=ErrorDomain.CONFLICT,
        code=1003,
        meta=ErrorMeta(
            http_status=409,
            i18n_key="error.conflict.duplicate_resource",
            level=ErrorLevel.WARNING,
        ),
    )


class IntegrationErrors:
    """外部服务集成错误"""

    EXTERNAL_API_FAILURE = ErrorCode(
        domain=ErrorDomain.INTEGRATION,
        code=1001,
        meta=ErrorMeta(
            http_status=502,
            i18n_key="error.integration.api_failure",
            level=ErrorLevel.ERROR,
        ),
    )


class SystemErrors:
    """500类 - 系统错误"""

    INTERNAL_SERVER_ERROR = ErrorCode(
        domain=ErrorDomain.INTERNAL,
        code=1001,
        meta=ErrorMeta(
            http_status=500,
            i18n_key="error.internal.server_error",
            level=ErrorLevel.CRITICAL,
        ),
    )

    SERVICE_UNAVAILABLE = ErrorCode(
        domain=ErrorDomain.INTERNAL,
        code=1002,
        meta=ErrorMeta(
            http_status=503,
            i18n_key="error.internal.service_unavailable",
            level=ErrorLevel.CRITICAL,
        ),
    )


class NotFoundErrors:
    """404类 - 资源不存在"""

    RESOURCE_NOT_FOUND = ErrorCode(
        domain=ErrorDomain.NOT_FOUND,
        code=1001,
        meta=ErrorMeta(
            http_status=404,
            i18n_key="error.not_found.resource",
            level=ErrorLevel.WARNING,
        ),
    )

    USER_NOT_FOUND = ErrorCode(
        domain=ErrorDomain.NOT_FOUND,
        code=1002,
        meta=ErrorMeta(
            http_status=404,
            i18n_key="error.not_found.user",
            level=ErrorLevel.WARNING,
        ),
    )

    ENDPOINT_NOT_FOUND = ErrorCode(
        domain=ErrorDomain.NOT_FOUND,
        code=1003,
        meta=ErrorMeta(
            http_status=404,
            i18n_key="error.not_found.endpoint",
            level=ErrorLevel.WARNING,
        ),
    )

    FILE_NOT_FOUND = ErrorCode(
        domain=ErrorDomain.NOT_FOUND,
        code=1004,
        meta=ErrorMeta(
            http_status=404,
            i18n_key="error.not_found.file",
            level=ErrorLevel.WARNING,
        ),
    )

    PAGE_NOT_FOUND = ErrorCode(
        domain=ErrorDomain.NOT_FOUND,
        code=1005,
        meta=ErrorMeta(
            http_status=404,
            i18n_key="error.not_found.page",
            level=ErrorLevel.WARNING,
        ),
    )


class DatabaseErrors:
    """数据库相关错误"""

    CONNECTION_FAILED = ErrorCode(
        domain=ErrorDomain.DATABASE,
        code=1001,
        meta=ErrorMeta(
            http_status=500,
            i18n_key="error.database.connection_failed",
            level=ErrorLevel.CRITICAL,
        ),
    )

    QUERY_FAILED = ErrorCode(
        domain=ErrorDomain.DATABASE,
        code=1002,
        meta=ErrorMeta(
            http_status=500,
            i18n_key="error.database.query_failed",
            level=ErrorLevel.ERROR,
        ),
    )

    TRANSACTION_FAILED = ErrorCode(
        domain=ErrorDomain.DATABASE,
        code=1003,
        meta=ErrorMeta(
            http_status=500,
            i18n_key="error.database.transaction_failed",
            level=ErrorLevel.ERROR,
        ),
    )

    CONSTRAINT_VIOLATION = ErrorCode(
        domain=ErrorDomain.DATABASE,
        code=1004,
        meta=ErrorMeta(
            http_status=400,
            i18n_key="error.database.constraint_violation",
            level=ErrorLevel.WARNING,
        ),
    )

    DUPLICATE_KEY = ErrorCode(
        domain=ErrorDomain.DATABASE,
        code=1005,
        meta=ErrorMeta(
            http_status=409,
            i18n_key="error.database.duplicate_key",
            level=ErrorLevel.WARNING,
        ),
    )

    TIMEOUT = ErrorCode(
        domain=ErrorDomain.DATABASE,
        code=1006,
        meta=ErrorMeta(
            http_status=504,
            i18n_key="error.database.timeout",
            level=ErrorLevel.ERROR,
        ),
    )


class ExternalServiceErrors:
    """外部服务错误"""

    SERVICE_UNAVAILABLE = ErrorCode(
        domain=ErrorDomain.EXTERNAL,
        code=1001,
        meta=ErrorMeta(
            http_status=503,
            i18n_key="error.external.service_unavailable",
            level=ErrorLevel.ERROR,
        ),
    )

    API_RATE_LIMITED = ErrorCode(
        domain=ErrorDomain.EXTERNAL,
        code=1002,
        meta=ErrorMeta(
            http_status=429,
            i18n_key="error.external.rate_limited",
            level=ErrorLevel.WARNING,
        ),
    )

    INVALID_RESPONSE = ErrorCode(
        domain=ErrorDomain.EXTERNAL,
        code=1003,
        meta=ErrorMeta(
            http_status=502,
            i18n_key="error.external.invalid_response",
            level=ErrorLevel.ERROR,
        ),
    )

    TIMEOUT = ErrorCode(
        domain=ErrorDomain.EXTERNAL,
        code=1004,
        meta=ErrorMeta(
            http_status=504,
            i18n_key="error.external.timeout",
            level=ErrorLevel.ERROR,
        ),
    )

    AUTHENTICATION_FAILED = ErrorCode(
        domain=ErrorDomain.EXTERNAL,
        code=1005,
        meta=ErrorMeta(
            http_status=502,
            i18n_key="error.external.auth_failed",
            level=ErrorLevel.ERROR,
        ),
    )


# RAG项目特有的错误类别
class RAGErrors:
    """RAG (检索增强生成) 相关错误"""

    DOCUMENT_PROCESSING_FAILED = ErrorCode(
        domain=ErrorDomain.BUSINESS,
        code=3001,
        meta=ErrorMeta(
            http_status=400,
            i18n_key="error.rag.document_processing_failed",
            level=ErrorLevel.ERROR,
        ),
    )

    EMBEDDING_GENERATION_FAILED = ErrorCode(
        domain=ErrorDomain.BUSINESS,
        code=3002,
        meta=ErrorMeta(
            http_status=500,
            i18n_key="error.rag.embedding_failed",
            level=ErrorLevel.ERROR,
        ),
    )

    VECTOR_SEARCH_FAILED = ErrorCode(
        domain=ErrorDomain.BUSINESS,
        code=3003,
        meta=ErrorMeta(
            http_status=500,
            i18n_key="error.rag.vector_search_failed",
            level=ErrorLevel.ERROR,
        ),
    )

    KNOWLEDGE_BASE_NOT_FOUND = ErrorCode(
        domain=ErrorDomain.NOT_FOUND,
        code=2001,
        meta=ErrorMeta(
            http_status=404,
            i18n_key="error.rag.knowledge_base_not_found",
            level=ErrorLevel.WARNING,
        ),
    )

    DOCUMENT_NOT_INDEXED = ErrorCode(
        domain=ErrorDomain.BUSINESS,
        code=3004,
        meta=ErrorMeta(
            http_status=400,
            i18n_key="error.rag.document_not_indexed",
            level=ErrorLevel.WARNING,
        ),
    )

    RETRIEVAL_FAILED = ErrorCode(
        domain=ErrorDomain.BUSINESS,
        code=3005,
        meta=ErrorMeta(
            http_status=500,
            i18n_key="error.rag.retrieval_failed",
            level=ErrorLevel.ERROR,
        ),
    )

    GENERATION_FAILED = ErrorCode(
        domain=ErrorDomain.BUSINESS,
        code=3006,
        meta=ErrorMeta(
            http_status=500,
            i18n_key="error.rag.generation_failed",
            level=ErrorLevel.ERROR,
        ),
    )

    CONTEXT_TOO_LARGE = ErrorCode(
        domain=ErrorDomain.BUSINESS,
        code=3007,
        meta=ErrorMeta(
            http_status=400,
            i18n_key="error.rag.context_too_large",
            level=ErrorLevel.WARNING,
        ),
    )

    UNSUPPORTED_FILE_TYPE = ErrorCode(
        domain=ErrorDomain.VALIDATION,
        code=2001,
        meta=ErrorMeta(
            http_status=400,
            i18n_key="error.rag.unsupported_file_type",
            level=ErrorLevel.WARNING,
        ),
    )

    FILE_TOO_LARGE = ErrorCode(
        domain=ErrorDomain.VALIDATION,
        code=2002,
        meta=ErrorMeta(
            http_status=413,
            i18n_key="error.rag.file_too_large",
            level=ErrorLevel.WARNING,
        ),
    )
