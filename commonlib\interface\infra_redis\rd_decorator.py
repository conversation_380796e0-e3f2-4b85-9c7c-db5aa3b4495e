import time
from functools import wraps
from inspect import iscoroutinefunction

from dependency_injector.wiring import inject

from commonlib.core.app_connections import get_app_connection_manager
from commonlib.core.tsif_logging import app_logger
from commonlib.utils.func_toolkit import fullname


def redis_cache_decorator(
    ttl: int = None,
    key_prefix: str = None,
    ignore_errors: bool = None,
    need_cache: bool = True,
    need_hash: bool = True,
    config: dict = None,
):
    """Redis缓存装饰器，用于缓存函数结果

    使用方式：
    1. 直接使用参数：@redis_cache_decorator(ttl=300, key_prefix="my_prefix")
    2. 通过InfraContainer辅助：@InfraContainer.with_cache(ttl_seconds=300)
    """
    # 如果提供了config参数，则使用config中的配置
    config_dict = config or {
        "ttl": ttl,
        "key_prefix": key_prefix,
        "ignore_errors": ignore_errors,
        "need_cache": need_cache,
        "need_hash": need_hash,
    }

    def decorator_wrapper(func):
        @wraps(func)
        async def wrapped(
            *args,
            **kwargs,
        ):
            decorator_instance = get_app_connection_manager().redis_cache_decorator()
            decorated_func = decorator_instance(config_dict)(func)
            return await decorated_func(*args, **kwargs)

        return wrapped

    return decorator_wrapper


def redis_distributed_task_preemption_decorator(name: str = None, px: int = 3000, nx: bool = True):
    """Redis分布式任务抢占装饰器

    确保同一时间只有一个应用实例执行该函数
    适用于不应并发执行的定时任务
    """

    def decorator_wrapper(func):
        @wraps(func)
        @inject
        async def wrapped(
            *args,
            **kwargs,
        ):
            # 使用指定名称或根据函数生成名称
            task_name = name or fullname(func)
            task_key = f"nx_schedule_job:{task_name}"
            task_value = time.perf_counter()
            redis_repo = get_app_connection_manager().decorator_redis_repo()

            # 尝试获取锁
            acquired = await redis_repo.set(task_key, task_value, px=px, nx=nx)

            if acquired:
                try:
                    # 根据函数类型执行
                    if iscoroutinefunction(func):
                        result = await func(*args, **kwargs)
                    else:
                        result = func(*args, **kwargs)
                    return result
                except Exception as e:
                    app_logger.error(
                        f"[分布式任务:{task_name}] 执行错误: {e}",
                        exception=True,
                    )
                    raise  # 重新抛出异常以便处理
                finally:
                    elapsed = time.perf_counter() - task_value
                    app_logger.info(f"[分布式任务:{task_name}] 执行耗时 {elapsed:.3f}秒")
            else:
                app_logger.info(f"[分布式任务:{task_name}] 跳过执行(其他实例正在运行)")
                return None

        return wrapped

    return decorator_wrapper
