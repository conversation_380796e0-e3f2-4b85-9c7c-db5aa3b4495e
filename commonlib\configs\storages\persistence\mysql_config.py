from typing import Any

from pydantic import BaseModel, ConfigDict, Field, MySQLDsn, NonNegativeInt


class MySQLConfig(BaseModel):
    """MySQL服务配置"""

    # 基础连接配置
    MYSQL_HOST: str = Field(
        default="localhost",
        description="数据库地址",
    )
    MYSQL_PORT: int = Field(default=3306, description="数据库端口", ge=1024, le=65535)
    MYSQL_USERNAME: str = Field(
        default="",
        description="数据库用户名",
    )
    MYSQL_PASSWORD: str = Field(
        default="",
        description="数据库密码",
    )
    MYSQL_DATABASE: str = Field(
        default="",
        description="数据库名称",
    )
    MYSQL_SCHEME: str = Field(
        default="mysql+aiomysql",
        description="数据库连接协议",
    )

    # 连接池配置
    MYSQL_POOL_SIZE: NonNegativeInt = Field(default=30, description="连接池大小", ge=1, le=1000)
    MYSQL_MAX_OVERFLOW: NonNegativeInt = Field(
        default=10, description="最大溢出连接数", ge=0, le=100
    )
    MYSQL_POOL_RECYCLE: NonNegativeInt = Field(
        default=3600, description="连接回收时间(秒)", ge=300, le=86400
    )
    MYSQL_POOL_PRE_PING: bool = Field(default=False, description="是否启用连接预检测")
    MYSQL_ECHO: bool = Field(default=False, description="是否开启SQL日志")

    @property
    def DSN(self) -> MySQLDsn:
        """构造MySQL DSN连接字符串"""
        return MySQLDsn.build(
            scheme=self.MYSQL_SCHEME,
            username=self.MYSQL_USERNAME,
            password=self.MYSQL_PASSWORD,
            host=self.MYSQL_HOST,
            port=self.MYSQL_PORT,
        )

    @property
    def MYSQL_POOL_CONFIG(self) -> dict[str, Any]:
        """连接池配置"""
        return {
            "pool_size": self.MYSQL_POOL_SIZE,
            "max_overflow": self.MYSQL_MAX_OVERFLOW,
            "pool_recycle": self.MYSQL_POOL_RECYCLE,
            "pool_pre_ping": self.MYSQL_POOL_PRE_PING,
            "echo": self.MYSQL_ECHO,
        }

    model_config = ConfigDict(
        validate_assignment=True,
        validate_default=True,
        json_encoders={MySQLDsn: str},
        extra="allow",
    )
