#!/usr/bin/env python3
"""运行所有代码质量检查"""

import subprocess
import sys
from pathlib import Path
from typing import List, Tuple

# 全局配置常量
DIR_CONFIG = {
    "core_dirs": ["services", "domain_common", "commonlib"],
    "exclude_dirs": ["__pycache__", ".git", ".idea", "venv"],
}


def get_project_root() -> Path:
    """动态获取项目根目录（通过.git或pyproject.toml标识）"""
    current_path = Path(__file__).resolve()
    while current_path != current_path.parent:
        if (current_path / ".git").exists() or (current_path / "pyproject.toml").exists():
            return current_path
        current_path = current_path.parent
    return current_path  # 回退到最顶层目录


def get_default_paths() -> list[str]:
    """获取默认要检查的路径列表"""
    project_root = get_project_root()

    # 核心目录（必须检查的）
    core_paths = [str(project_root / d) for d in DIR_CONFIG["core_dirs"]]

    # 自动发现的其他目录（可选）
    extra_paths = [
        str(p)
        for p in project_root.iterdir()
        if p.is_dir()
        and p.name not in DIR_CONFIG["exclude_dirs"]
        and p.name not in DIR_CONFIG["core_dirs"]  # 避免重复
    ]

    return core_paths + extra_paths


def run_script(script_name: str, paths: List[str]) -> Tuple[int, str]:
    """运行指定的质量检查脚本

    Args:
        script_name: 脚本名称
        paths: 要检查的路径列表

    Returns:
        (返回码, 输出信息)
    """
    script_path = Path(__file__).parent / "format_script" / f"{script_name}.py"
    if not script_path.exists():
        return 1, f"Error: Script not found: {script_path}"

    try:
        result = subprocess.run(
            [sys.executable, str(script_path), *paths],
            capture_output=True,
            text=True,
            check=False,
        )
        return result.returncode, result.stdout
    except Exception as e:
        return 1, f"Error running {script_name}: {e}"


def main():
    """主函数"""
    paths = sys.argv[1:] or get_default_paths()
    # 确保路径存在
    for path in paths:
        if not Path(path).exists():
            print(f"Error: Path does not exist: {path}")
            sys.exit(1)

    scripts = ["format", "lint", "typecheck", "cleanup_imports"]
    exit_code = 0

    for script in scripts:
        print(f"\n{'=' * 20} Running {script} {'=' * 20}")
        code, output = run_script(script, paths)
        print(output)
        exit_code = exit_code or code

    if exit_code:
        print("\n❌ Some checks failed!")
    else:
        print("\n✅ All checks passed!")

    sys.exit(exit_code)


if __name__ == "__main__":
    main()
