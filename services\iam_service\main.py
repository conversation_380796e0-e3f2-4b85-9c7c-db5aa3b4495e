"""
IAM 服务应用入口

基于项目统一的 DI 容器设计，提供用户与权限管理服务
"""

from contextlib import asynccontextmanager

from container import ServiceContainer
from fastapi import FastAPI
from routes import api_router
from tasks.scheduler import dispatch_scheduler

from commonlib.configs.base_setting import AppSettings
from commonlib.core.app_config import AppConfig, setup_app_config
from commonlib.core.app_connections import setup_connection_manager, shutdown_connection_manager, \
    app_local_connection_manager
from commonlib.core.tsif_logging import app_logger
from domain_common.app_builder.default_app_builder import AppBuilder

# 全局配置与服务容器
config = setup_app_config()
services = ServiceContainer()

# 初始化任务调度器
dispatch_scheduler()

# 依赖注入模块绑定
services.wire(
    modules=[
        "routes.tenants",
        "routes.users",
        "routes.auth",
        "routes.rbac",
        "routes.roles",
        "routes.permissions",
        "routes.audit",
        "routes.advanced_security",
        "routes.system",
        "routes.system_config",
    ]
)


@asynccontextmanager
async def lifespan(local_app: FastAPI):
    """增强的生命周期管理（带健康检查）"""
    try:
        await setup_connection_manager(config)
        await services.init_resources()

        boot_service = await services.system_boot_service()
        await boot_service.initialize_system()

        AppBuilder.start_app_scheduler()

        yield
    except Exception as e:
        app_logger.exception(f"Startup failed: {str(e)}", exception=True)
        raise
    finally:
        AppBuilder.close_app_scheduler()
        await shutdown_connection_manager()


def create_app(app_config: AppConfig) -> FastAPI:
    """优化后的应用工厂"""
    settings: AppSettings = app_config.config
    app_info = settings.application

    local_app = FastAPI(
        debug=settings.debug,
        title=app_info.title,
        description=app_info.description,
        docs_url=app_info.docs_url,
        openapi_url=app_info.openapi_url,
        redoc_url=app_info.redoc_url,
        lifespan=lifespan,
    )

    # 配置应用
    AppBuilder.register_exception_handlers(local_app, settings)
    AppBuilder.setup_middlewares(local_app, settings)
    AppBuilder.register_health_router(local_app)

    return local_app


# 创建应用实例
app = create_app(config)

# 注册路由
app.include_router(api_router)

if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8089, log_config=None)
