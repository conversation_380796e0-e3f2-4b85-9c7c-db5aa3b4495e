"""
IAM 服务引导层

统一管理配置加载、DI容器构建、模块wiring、资源生命周期等公共逻辑
"""

from contextlib import asynccontextmanager
from typing import Tuple, Literal

from container import ServiceContainer
from module_wiring import WIRE_MODULES
from tasks.scheduler import dispatch_scheduler

from commonlib.core.app_config import AppConfig, setup_app_config
from commonlib.core.app_connections import setup_connection_manager, shutdown_connection_manager
from commonlib.core.tsif_logging import app_logger
from domain_common.app_builder.default_app_builder import AppBuilder


def load_config_and_container() -> Tuple[AppConfig, ServiceContainer]:
    """
    加载配置和创建服务容器
    
    Returns:
        配置对象和服务容器的元组
    """
    config = setup_app_config()
    services = ServiceContainer()
    return config, services


def wire_modules(services: ServiceContainer) -> None:
    """
    统一进行模块依赖注入wiring
    
    Args:
        services: 服务容器实例
    """
    services.wire(modules=WIRE_MODULES)
    app_logger.info(f"Wired modules: {WIRE_MODULES}")


@asynccontextmanager
async def runtime(
    config: AppConfig, 
    services: ServiceContainer, 
    role: Literal["api", "worker"] = "api"
):
    """
    统一的运行时生命周期管理
    
    Args:
        config: 应用配置
        services: 服务容器
        role: 运行角色，api或worker
    """
    try:
        # 初始化连接管理器
        await setup_connection_manager(config)
        
        # 初始化服务容器资源
        await services.init_resources()
        
        # API模式特有的初始化
        if role == "api":
            # 初始化任务调度器
            dispatch_scheduler()
            
            # 系统引导初始化
            boot_service = await services.system_boot_service()
            await boot_service.initialize_system()
            
            # 启动应用调度器
            AppBuilder.start_app_scheduler()
            
        app_logger.info(f"Runtime initialized for role: {role}")
        
        yield
        
    except Exception as e:
        app_logger.exception(f"Runtime startup failed for role {role}: {str(e)}", exception=True)
        raise
    finally:
        # 清理资源
        if role == "api":
            AppBuilder.close_app_scheduler()
            
        await services.shutdown_resources()
        await shutdown_connection_manager()
        
        app_logger.info(f"Runtime shutdown complete for role: {role}")


def setup_bootstrap(role: Literal["api", "worker"] = "api") -> Tuple[AppConfig, ServiceContainer]:
    """
    完整的引导设置
    
    Args:
        role: 运行角色
        
    Returns:
        配置对象和服务容器的元组
    """
    # 加载配置和容器
    config, services = load_config_and_container()
    
    # 进行模块wiring
    wire_modules(services)
    
    return config, services
