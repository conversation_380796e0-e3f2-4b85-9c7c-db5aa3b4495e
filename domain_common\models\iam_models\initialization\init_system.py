#!/usr/bin/env python3
"""
系统初始化命令行工具

用于系统首次部署时的初始化操作
"""

import argparse
import asyncio
import json
import sys
from pathlib import Path
from typing import Any, Dict

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from domain_common.models.iam_models.initialization.system_bootstrap import SystemBootstrap

# 添加项目根目录到Python路径



sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent.parent))


class SystemInitializer:
    """系统初始化器"""

    def __init__(self, database_url: str):
        self.database_url = database_url
        self.engine = None
        self.session_factory = None

    async def setup_database(self):
        """设置数据库连接"""
        self.engine = create_async_engine(self.database_url, echo=False, pool_pre_ping=True)

        self.session_factory = sessionmaker(
            self.engine, class_=AsyncSession, expire_on_commit=False
        )

    async def initialize_system(
        self,
        admin_username: str = "platform_admin",
        admin_email: str = "<EMAIL>",
        admin_password: str = None,
        force: bool = False,
    ) -> Dict[str, Any]:
        """执行系统初始化"""

        if not self.session_factory:
            await self.setup_database()

        async with self.session_factory() as session:
            bootstrap = SystemBootstrap(session)

            # 检查是否已经初始化
            if not force and await bootstrap._is_system_initialized():
                return {
                    "status": "already_initialized",
                    "message": "系统已经初始化，使用 --force 参数强制重新初始化",
                }

            try:
                result = await bootstrap.initialize_system(
                    admin_username=admin_username,
                    admin_email=admin_email,
                    admin_password=admin_password,
                )

                result["status"] = "success"
                result["message"] = "系统初始化成功"

                return result

            except Exception as e:
                return {"status": "error", "message": f"系统初始化失败: {str(e)}"}

    async def cleanup(self):
        """清理资源"""
        if self.engine:
            await self.engine.dispose()


def print_result(result: Dict[str, Any], output_format: str = "text"):
    """打印初始化结果"""

    if output_format == "json":
        print(json.dumps(result, indent=2, ensure_ascii=False))
        return

    # 文本格式输出
    status = result.get("status", "unknown")
    message = result.get("message", "")

    if status == "success":
        print("✅ 系统初始化成功!")
        print(f"📅 初始化时间: {result.get('initialized_at', 'N/A')}")

        # 管理员信息
        admin_user = result.get("admin_user", {})
        if admin_user:
            print("\n👤 超级管理员信息:")
            print(f"   用户ID: {admin_user.get('user_id', 'N/A')}")
            print(f"   用户名: {admin_user.get('username', 'N/A')}")
            print(f"   邮箱: {admin_user.get('email', 'N/A')}")

        # 生成的密码
        generated_password = result.get("generated_password")
        if generated_password:
            print(f"\n🔑 生成的管理员密码: {generated_password}")
            print("⚠️  请妥善保存此密码，并在首次登录后修改!")

        # 创建的角色
        roles = result.get("roles", [])
        if roles:
            print(f"\n🎭 创建的系统角色 ({len(roles)}个):")
            for role in roles:
                print(f"   - {role}")

        # 创建的权限
        permissions = result.get("permissions", [])
        if permissions:
            print(f"\n🔐 创建的系统权限 ({len(permissions)}个):")
            for _, perm in enumerate(permissions[:5]):  # 只显示前5个
                print(f"   - {perm}")
            if len(permissions) > 5:
                print(f"   ... 还有 {len(permissions) - 5} 个权限")

        # 创建的用户组
        groups = result.get("groups", [])
        if groups:
            print(f"\n👥 创建的系统用户组 ({len(groups)}个):")
            for group in groups:
                print(f"   - {group}")

        print("\n🎉 系统已准备就绪，可以开始创建租户和用户!")

    elif status == "already_initialized":
        print("ℹ️  系统已经初始化")
        print(f"   {message}")

    elif status == "error":
        print("❌ 系统初始化失败")
        print(f"   错误信息: {message}")

    else:
        print(f"❓ 未知状态: {status}")
        print(f"   消息: {message}")


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="IAM系统初始化工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 使用默认配置初始化
  python init_system.py --database-url "postgresql+asyncpg://user:pass@localhost/db"
  
  # 指定管理员信息
  python init_system.py \\
    --database-url "postgresql+asyncpg://user:pass@localhost/db" \\
    --admin-username "admin" \\
    --admin-email "<EMAIL>" \\
    --admin-password "SecurePassword123!"
  
  # 强制重新初始化
  python init_system.py \\
    --database-url "postgresql+asyncpg://user:pass@localhost/db" \\
    --force
  
  # JSON格式输出
  python init_system.py \\
    --database-url "postgresql+asyncpg://user:pass@localhost/db" \\
    --output json
        """,
    )

    parser.add_argument(
        "--database-url",
        required=True,
        help="数据库连接URL (例如: postgresql+asyncpg://user:pass@localhost/db)",
    )

    parser.add_argument(
        "--admin-username",
        default="platform_admin",
        help="超级管理员用户名 (默认: platform_admin)",
    )

    parser.add_argument(
        "--admin-email",
        default="<EMAIL>",
        help="超级管理员邮箱 (默认: <EMAIL>)",
    )

    parser.add_argument("--admin-password", help="超级管理员密码 (如果不指定则自动生成)")

    parser.add_argument("--force", action="store_true", help="强制重新初始化 (即使系统已经初始化)")

    parser.add_argument(
        "--output",
        choices=["text", "json"],
        default="text",
        help="输出格式 (默认: text)",
    )

    parser.add_argument("--quiet", action="store_true", help="静默模式，只输出错误信息")

    args = parser.parse_args()

    # 创建初始化器
    initializer = SystemInitializer(args.database_url)

    try:
        if not args.quiet:
            print("🚀 开始系统初始化...")
            print(
                f"📊 数据库: {args.database_url.split('@')[-1] if '@' in args.database_url else args.database_url}"
            )
            print(f"👤 管理员: {args.admin_username} ({args.admin_email})")
            if args.force:
                print("⚠️  强制模式: 将重新初始化系统")
            print()

        # 执行初始化
        result = await initializer.initialize_system(
            admin_username=args.admin_username,
            admin_email=args.admin_email,
            admin_password=args.admin_password,
            force=args.force,
        )

        # 输出结果
        if not args.quiet:
            print_result(result, args.output)
        elif result.get("status") == "error":
            print(f"错误: {result.get('message', '未知错误')}", file=sys.stderr)
            sys.exit(1)
        elif args.output == "json":
            print(json.dumps(result, indent=2, ensure_ascii=False))

        # 设置退出码
        if result.get("status") == "error":
            sys.exit(1)
        elif result.get("status") == "already_initialized" and not args.force:
            sys.exit(2)  # 已初始化
        else:
            sys.exit(0)  # 成功

    except KeyboardInterrupt:
        print("\n❌ 用户中断操作", file=sys.stderr)
        sys.exit(130)

    except Exception as e:
        print(f"❌ 意外错误: {str(e)}", file=sys.stderr)
        sys.exit(1)

    finally:
        await initializer.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
