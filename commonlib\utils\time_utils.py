from dataclasses import dataclass
from datetime import date, datetime, time, timedelta
from enum import Enum
from functools import lru_cache
from typing import List, Optional, Union
from zoneinfo import ZoneInfo


class TimeFormat(Enum):
    """预定义时间格式枚举"""

    DATE = "%Y-%m-%d"
    TIME = "%H:%M"
    TIME_WITH_SECONDS = "%H:%M:%S"
    DATETIME = "%Y-%m-%d %H:%M:%S"
    ISO8601 = "%Y-%m-%dT%H:%M:%S%z"


@dataclass(frozen=True)
class TimeRange:
    """时间范围对象"""

    start: datetime
    end: datetime


class TimeUtils:
    DEFAULT_TIMEZONE = ZoneInfo("Asia/Shanghai")

    @staticmethod
    def now() -> datetime:
        """获取当前时间（带时区）"""
        return datetime.now(TimeUtils.DEFAULT_TIMEZONE)

    @staticmethod
    def today() -> date:
        """获取当前日期"""
        return TimeUtils.now().date()

    @classmethod
    def start_of_day(cls, dt: Optional[Union[date, datetime]] = None) -> datetime:
        """获取某天的开始时间（00:00:00）"""
        if dt is None:
            dt = cls.today()
        elif isinstance(dt, datetime):
            dt = dt.date()
        return datetime.combine(dt, time.min).replace(tzinfo=cls.DEFAULT_TIMEZONE)

    @classmethod
    def end_of_day(cls, dt: Optional[Union[date, datetime]] = None) -> datetime:
        """获取某天的结束时间（23:59:59）"""
        return cls.start_of_day(dt) + timedelta(days=1) - timedelta(seconds=1)

    @classmethod
    def format_datetime(
        cls, dt: datetime, fmt: Union[str, TimeFormat] = TimeFormat.DATETIME
    ) -> str:
        """格式化时间为字符串"""
        if not dt.tzinfo:
            dt = dt.replace(tzinfo=cls.DEFAULT_TIMEZONE)
        fmt_str = fmt.value if isinstance(fmt, TimeFormat) else fmt
        return dt.strftime(fmt_str)

    @classmethod
    def parse_datetime(
        cls, datetime_str: str, fmt: Optional[Union[str, TimeFormat]] = None
    ) -> datetime:
        """解析字符串为datetime对象"""
        if fmt is None:
            fmt = cls._detect_format(datetime_str)
        fmt_str = fmt.value if isinstance(fmt, TimeFormat) else fmt
        dt = datetime.strptime(datetime_str, fmt_str)
        return dt.replace(tzinfo=cls.DEFAULT_TIMEZONE)

    @staticmethod
    @lru_cache(maxsize=32)
    def _detect_format(datetime_str: str) -> TimeFormat:
        """自动检测时间字符串格式（带缓存）"""
        length = len(datetime_str)
        if length == 10:
            return TimeFormat.DATE
        elif length == 5:
            return TimeFormat.TIME
        elif length == 8:
            return TimeFormat.TIME_WITH_SECONDS
        elif length == 19:
            return TimeFormat.DATETIME
        elif "T" in datetime_str and "Z" in datetime_str:
            return TimeFormat.ISO8601
        raise ValueError(f"Cannot detect format for: {datetime_str}")

    @classmethod
    def parse_time(cls, time_str: str, reference_date: Optional[date] = None) -> datetime:
        """解析时间字符串为datetime对象"""
        time_fmt = cls._detect_format(time_str)
        time_obj = datetime.strptime(time_str, time_fmt.value).time()
        ref_date = reference_date or cls.today()
        return datetime.combine(ref_date, time_obj).replace(tzinfo=cls.DEFAULT_TIMEZONE)

    @classmethod
    def month_range(cls, dt: Optional[datetime] = None, last_month: bool = False) -> TimeRange:
        """获取月份时间范围（当前月或上个月）"""
        dt = dt or cls.now()
        if last_month:
            first_day = (dt.replace(day=1) - timedelta(days=1)).replace(day=1)
        else:
            first_day = dt.replace(day=1)

        next_month = first_day.replace(day=28) + timedelta(days=4)
        last_day = next_month - timedelta(days=next_month.day)

        return TimeRange(start=cls.start_of_day(first_day), end=cls.end_of_day(last_day))

    @classmethod
    def days_between(
        cls,
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        date_format: Optional[str] = None,
    ) -> int:
        """计算两个日期之间的天数差"""
        if isinstance(start_date, str):
            start_date = cls.parse_datetime(start_date, date_format)
        if isinstance(end_date, str):
            end_date = cls.parse_datetime(end_date, date_format)

        if isinstance(start_date, datetime):
            start_date = start_date.date()
        if isinstance(end_date, datetime):
            end_date = end_date.date()

        return (end_date - start_date).days

    @classmethod
    def generate_date_series(
        cls,
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        reverse: bool = False,
        date_format: Optional[str] = None,
    ) -> List[str]:
        """生成日期序列"""
        if isinstance(start_date, str):
            start_date = cls.parse_datetime(start_date, date_format)
        if isinstance(end_date, str):
            end_date = cls.parse_datetime(end_date, date_format)

        if isinstance(start_date, datetime):
            start_date = start_date.date()
        if isinstance(end_date, datetime):
            end_date = end_date.date()

        days = (end_date - start_date).days + 1
        date_series = [
            (start_date + timedelta(days=i)).strftime(TimeFormat.DATE.value) for i in range(days)
        ]
        return date_series[::-1] if reverse else date_series


if __name__ == "__main__":
    # 示例用法
    print("当前时间:", TimeUtils.now())
    print("今天日期:", TimeUtils.today())
    print("今日开始时间:", TimeUtils.start_of_day())
    print("今日结束时间:", TimeUtils.end_of_day())

    parsed_time = TimeUtils.parse_time("14:30")
    print("解析时间:", parsed_time)

    current_month = TimeUtils.month_range()
    print("本月范围:", current_month.start, "to", current_month.end)

    last_month = TimeUtils.month_range(last_month=True)
    print("上月范围:", last_month.start, "to", last_month.end)

    print("日期差:", TimeUtils.days_between("2023-01-01", "2023-12-31"))
    print("日期序列:", TimeUtils.generate_date_series("2023-12-25", "2023-12-31"))
