"""
IAM 任务模型

包含批量任务等异步处理相关实体
"""

from datetime import datetime
from typing import Optional

from sqlalchemy import Index, Text
from sqlalchemy.orm import Mapped, mapped_column

from domain_common.models.base_model import Base
from domain_common.models.constants import CommonStatus, JSONType
from domain_common.models.fields import Fields
from domain_common.models.mixins import StatusManager


class BatchTask(Base):
    """批量任务模型

    用于跟踪和管理各种批量操作任务的状态和结果
    """

    __tablename__ = "batch_tasks"

    # 重写主键为 task_id
    task_id: Mapped[str] = Fields.uuid_primary_key(doc="任务ID")

    # 多租户字段
    tenant_id: Mapped[str] = Fields.tenant()

    # 任务基本信息字段
    task_name: Mapped[str] = Fields.name(doc="任务名称")
    task_type: Mapped[str] = Fields.code(doc="任务类型")
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True, doc="任务描述")

    # 任务配置和结果字段
    config: Mapped[Optional[JSONType]] = Fields.json_field(default={}, doc="任务配置参数")
    result: Mapped[Optional[JSONType]] = Fields.json_field(default={}, doc="任务执行结果")

    # 状态字段
    status: Mapped[str] = Fields.status(default=CommonStatus.PENDING)

    # 任务执行信息字段
    started_at: Mapped[Optional[datetime]] = mapped_column(nullable=True, doc="任务开始时间")
    completed_at: Mapped[Optional[datetime]] = mapped_column(nullable=True, doc="任务完成时间")
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True, doc="错误信息")

    # 创建者字段
    created_by: Mapped[Optional[str]] = Fields.created_by()

    __table_args__ = (
        Index("idx_batch_tasks_tenant_type", "tenant_id", "task_type"),
        Index("idx_batch_tasks_status", "status"),
        Index("idx_batch_tasks_created_by", "created_by"),
        Index("idx_batch_tasks_created_at", "created_at"),
        Index("idx_batch_tasks_tenant_status", "tenant_id", "status"),
    )

    @property
    def status_manager(self) -> StatusManager:
        """获取状态管理器"""
        return StatusManager(self)

    def __repr__(self) -> str:
        return f"<BatchTask(task_id={self.task_id}, task_type={self.task_type}, status={self.status}, tenant_id={self.tenant_id})>"
