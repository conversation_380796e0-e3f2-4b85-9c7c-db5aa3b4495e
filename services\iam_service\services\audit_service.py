"""
审计服务

提供审计日志的创建、查询、统计和导出功能
支持安全事件记录、操作追踪和合规性审计
"""

import csv
import io
import json
import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Type

from sqlalchemy import and_, asc, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from commonlib.exceptions.exceptions import BusinessError, ValidationError
from commonlib.storages.persistence.redis.repository import RedisRepository
from domain_common.models.iam_models import AuditLog, AuditLogBuilder, Tenant, User


class AuditService:
    """审计服务类"""

    def __init__(
        self,
        session: AsyncSession,
        redis_repo: RedisRepository,
        user_model: Type[User],
        tenant_model: Type[Tenant],
        audit_log_model: Type[AuditLog],
    ):
        # 数据库会话和缓存
        self.session = session
        self.redis_repo = redis_repo

        # 业务模型
        self.user_model = user_model
        self.tenant_model = tenant_model
        self.audit_log_model = audit_log_model

        # 缓存键前缀
        self.stats_cache_prefix = "audit_stats:"
        self.export_cache_prefix = "audit_export:"

        # 风险级别映射
        self.risk_levels = {"low": 1, "medium": 2, "high": 3, "critical": 4}

        # 操作状态
        self.statuses = ["success", "failed", "warning"]

    # ===== 审计日志管理 =====

    async def create_audit_log(
        self,
        tenant_id: str,
        user_id: Optional[str],
        action: str,
        resource_type: str,
        resource_id: Optional[str] = None,
        description: str = "",
        details: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        status: str = "success",
    ) -> Dict[str, Any]:
        """
        创建审计日志

        记录系统操作和安全事件
        """
        try:
            # 验证参数 - 使用审计模型的result字段值
            valid_results = ["success", "failure", "error"]
            if status not in valid_results:
                raise ValidationError(f"无效的状态: {status}，有效值: {valid_results}")

            # 使用AuditLogBuilder创建审计日志
            audit_log = AuditLogBuilder.create_audit_log(
                tenant_id=tenant_id,
                user_id=user_id,
                action=action,
                resource_type=resource_type,
                resource_id=resource_id,
                result=status,  # 使用result字段而不是status
                details=details or {},
                ip_address=ip_address,
                user_agent=user_agent,
                error_message=description if status in ["failure", "error"] else None,
            )

            # 设置会话上下文（如果有的话）
            session_data = {}
            if ip_address:
                session_data["ip_address"] = ip_address
            if user_agent:
                session_data["user_agent"] = user_agent
            if session_data:
                audit_log.set_session_context(session_data)

            self.session.add(audit_log)
            await self.session.commit()

            # 如果是失败或错误操作，发送告警
            if status in ["failure", "error"]:
                await self._send_security_alert(audit_log)

            # 清除相关统计缓存
            await self._clear_stats_cache(tenant_id)

            return {
                "log_id": str(audit_log.id),  # 使用数据库ID
                "created_at": audit_log.created_at.isoformat(),
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"创建审计日志失败: {str(e)}")

    async def query_audit_logs(
        self,
        tenant_id: str,
        user_id: Optional[str] = None,
        action: Optional[str] = None,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        start_time: Optional[str] = None,
        end_time: Optional[str] = None,
        ip_address: Optional[str] = None,
        status: Optional[str] = None,
        risk_level: Optional[str] = None,
        page: int = 1,
        page_size: int = 20,
        order_by: str = "created_at",
        order_direction: str = "desc",
    ) -> Dict[str, Any]:
        """
        查询审计日志

        支持多条件查询和分页
        """
        try:
            # 构建查询条件
            conditions = [self.audit_log_model.tenant_id == tenant_id]

            if user_id:
                conditions.append(self.audit_log_model.user_id == user_id)

            if action:
                conditions.append(self.audit_log_model.action == action)

            if resource_type:
                conditions.append(self.audit_log_model.resource_type == resource_type)

            if resource_id:
                conditions.append(self.audit_log_model.resource_id == resource_id)

            if start_time:
                start_dt = datetime.fromisoformat(start_time)
                conditions.append(self.audit_log_model.created_at >= start_dt)

            if end_time:
                end_dt = datetime.fromisoformat(end_time)
                conditions.append(self.audit_log_model.created_at <= end_dt)

            if ip_address:
                conditions.append(self.audit_log_model.ip_address == ip_address)

            if status:
                # 使用result字段而不是status
                conditions.append(self.audit_log_model.result == status)

            # 查询总数
            count_stmt = select(func.count(self.audit_log_model.id)).where(and_(*conditions))
            total_result = await self.session.execute(count_stmt)
            total = total_result.scalar()

            # 构建排序
            order_column = getattr(self.audit_log_model, order_by, self.audit_log_model.created_at)
            order_func = desc if order_direction == "desc" else asc

            # 分页查询
            offset = (page - 1) * page_size
            stmt = (
                select(self.audit_log_model)
                .where(and_(*conditions))
                .order_by(order_func(order_column))
                .offset(offset)
                .limit(page_size)
            )
            result = await self.session.execute(stmt)
            logs = result.scalars().all()

            # 构建日志列表
            log_list = []
            for log in logs:
                # 从会话上下文中获取用户名
                username = None
                if log.user_id:
                    user = await self._get_user_by_id(log.user_id)
                    if user:
                        username = user.username

                log_info = {
                    "log_id": str(log.id),
                    "tenant_id": log.tenant_id,
                    "user_id": log.user_id,
                    "username": username,
                    "action": log.action,
                    "resource_type": log.resource_type,
                    "resource_id": log.resource_id,
                    "description": log.error_message or "",  # 使用error_message作为描述
                    "details": log.details,
                    "ip_address": log.ip_address,
                    "user_agent": log.user_agent,
                    "status": log.result,  # 使用result字段
                    "risk_level": self._calculate_risk_level(log),  # 计算风险级别
                    "created_at": log.created_at.isoformat(),
                }
                log_list.append(log_info)

            return {
                "logs": log_list,
                "total": total,
                "page": page,
                "page_size": page_size,
                "has_next": offset + page_size < total,
            }

        except Exception as e:
            raise BusinessError(f"查询审计日志失败: {str(e)}")

    async def get_audit_statistics(
        self,
        tenant_id: str,
        start_time: Optional[str] = None,
        end_time: Optional[str] = None,
        group_by: str = "day",
        metrics: List[str] = None,
    ) -> Dict[str, Any]:
        """
        获取审计统计

        获取审计日志的统计分析数据
        """
        try:
            # 设置默认时间范围（最近30天）
            if not start_time:
                start_dt = datetime.utcnow() - timedelta(days=30)
            else:
                start_dt = datetime.fromisoformat(start_time)

            if not end_time:
                end_dt = datetime.utcnow()
            else:
                end_dt = datetime.fromisoformat(end_time)

            # 检查缓存
            cache_key = (
                f"{self.stats_cache_prefix}{tenant_id}:{start_dt.date()}:{end_dt.date()}:{group_by}"
            )
            cached_stats = await self.redis_repo.get(cache_key)
            if cached_stats:
                return cached_stats

            # 基础条件
            base_conditions = [
                self.audit_log_model.tenant_id == tenant_id,
                self.audit_log_model.created_at >= start_dt,
                self.audit_log_model.created_at <= end_dt,
            ]

            # 总体统计
            total_logs = await self._get_count_by_conditions(base_conditions)
            success_logs = await self._get_count_by_conditions(
                base_conditions + [self.audit_log_model.result == "success"]
            )
            failed_logs = await self._get_count_by_conditions(
                base_conditions + [self.audit_log_model.result.in_(["failure", "error"])]
            )
            # 高风险日志基于操作类型和结果判断
            high_risk_logs = await self._get_count_by_conditions(
                base_conditions + [self.audit_log_model.result.in_(["failure", "error"])]
            )

            # 热门操作
            top_actions = await self._get_top_actions(base_conditions)

            # 活跃用户
            top_users = await self._get_top_users(base_conditions)

            # 风险分布
            risk_distribution = await self._get_risk_distribution(base_conditions)

            # 时间序列数据
            time_series = await self._get_time_series_data(
                base_conditions, group_by, start_dt, end_dt
            )

            stats_data = {
                "period": f"{start_dt.date()} - {end_dt.date()}",
                "total_logs": total_logs,
                "success_logs": success_logs,
                "failed_logs": failed_logs,
                "high_risk_logs": high_risk_logs,
                "top_actions": top_actions,
                "top_users": top_users,
                "risk_distribution": risk_distribution,
                "time_series": time_series,
            }

            # 缓存统计数据（1小时）
            await self.redis_repo.set(cache_key, stats_data, ttl=3600)

            return stats_data

        except Exception as e:
            raise BusinessError(f"获取审计统计失败: {str(e)}")

    async def export_audit_logs(
        self,
        tenant_id: str,
        user_id: Optional[str] = None,
        start_time: Optional[str] = None,
        end_time: Optional[str] = None,
        format: str = "csv",
        include_details: bool = True,
    ) -> Dict[str, Any]:
        """
        导出审计日志

        创建审计日志导出任务
        """
        try:
            # 生成导出任务ID
            export_id = f"export_{uuid.uuid4()}"

            # 构建查询条件
            conditions = [self.audit_log_model.tenant_id == tenant_id]

            if user_id:
                conditions.append(self.audit_log_model.user_id == user_id)

            if start_time:
                start_dt = datetime.fromisoformat(start_time)
                conditions.append(self.audit_log_model.created_at >= start_dt)

            if end_time:
                end_dt = datetime.fromisoformat(end_time)
                conditions.append(self.audit_log_model.created_at <= end_dt)

            # 查询数据
            stmt = (
                select(self.audit_log_model)
                .where(and_(*conditions))
                .order_by(desc(self.audit_log_model.created_at))
            )
            result = await self.session.execute(stmt)
            logs = result.scalars().all()

            # 生成文件
            file_content, file_name, file_size = await self._generate_export_file(
                logs, format, include_details
            )

            # 存储文件到临时位置（实际应该存储到文件系统或对象存储）
            download_url = f"/api/v1/audit/exports/{export_id}/download"

            # 缓存导出信息
            export_info = {
                "export_id": export_id,
                "file_content": file_content,
                "file_name": file_name,
                "file_size": file_size,
                "record_count": len(logs),
                "created_at": datetime.utcnow().isoformat(),
                "expires_at": (datetime.utcnow() + timedelta(hours=24)).isoformat(),
            }

            await self.redis_repo.set(
                f"{self.export_cache_prefix}{export_id}",
                export_info,
                ttl=86400,  # 24小时过期
            )

            return {
                "export_id": export_id,
                "download_url": download_url,
                "file_name": file_name,
                "file_size": file_size,
                "record_count": len(logs),
                "created_at": export_info["created_at"],
                "expires_at": export_info["expires_at"],
            }

        except Exception as e:
            raise BusinessError(f"导出审计日志失败: {str(e)}")

    # ===== 辅助方法 =====

    async def _get_user_by_id(self, user_id: str):
        """根据ID获取用户"""
        stmt = select(self.user_model).where(self.user_model.user_id == user_id)
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def _send_security_alert(self, audit_log):
        """发送安全告警"""
        # TODO: 实现安全告警机制
        # 可以发送邮件、短信或推送通知
        print(f"安全告警: {audit_log.action} - {audit_log.risk_level} - {audit_log.description}")

    async def _clear_stats_cache(self, tenant_id: str):
        """清除统计缓存"""
        pattern = f"{self.stats_cache_prefix}{tenant_id}:*"
        keys = await self.redis_repo.scan_keys(pattern)
        if keys:
            await self.redis_repo.delete(*keys)

    async def _get_count_by_conditions(self, conditions: List) -> int:
        """根据条件获取计数"""
        stmt = select(func.count(self.audit_log_model.id)).where(and_(*conditions))
        result = await self.session.execute(stmt)
        return result.scalar() or 0

    async def _get_top_actions(
        self, base_conditions: List, limit: int = 10
    ) -> List[Dict[str, Any]]:
        """获取热门操作"""
        stmt = (
            select(
                self.audit_log_model.action,
                func.count(self.audit_log_model.id).label("count"),
            )
            .where(and_(*base_conditions))
            .group_by(self.audit_log_model.action)
            .order_by(desc("count"))
            .limit(limit)
        )
        result = await self.session.execute(stmt)
        return [{"action": row[0], "count": row[1]} for row in result.fetchall()]

    async def _get_top_users(self, base_conditions: List, limit: int = 10) -> List[Dict[str, Any]]:
        """获取活跃用户"""
        stmt = (
            select(
                self.audit_log_model.user_id,
                func.count(self.audit_log_model.id).label("count"),
            )
            .where(and_(*base_conditions))
            .where(self.audit_log_model.user_id.is_not(None))
            .group_by(self.audit_log_model.user_id)
            .order_by(desc("count"))
            .limit(limit)
        )
        result = await self.session.execute(stmt)

        # 获取用户名
        user_list = []
        for row in result.fetchall():
            user_id = row[0]
            count = row[1]

            # 查询用户名
            username = "未知用户"
            if user_id:
                user = await self._get_user_by_id(user_id)
                if user:
                    username = user.username

            user_list.append({"user_id": user_id, "username": username, "count": count})

        return user_list

    async def _get_risk_distribution(self, base_conditions: List) -> Dict[str, int]:
        """获取风险分布（基于操作结果）"""
        stmt = (
            select(
                self.audit_log_model.result,
                func.count(self.audit_log_model.id).label("count"),
            )
            .where(and_(*base_conditions))
            .group_by(self.audit_log_model.result)
        )
        result = await self.session.execute(stmt)

        # 将结果映射到风险级别
        distribution = {"low": 0, "medium": 0, "high": 0, "critical": 0}
        for row in result.fetchall():
            result_type = row[0]
            count = row[1]

            if result_type == "success":
                distribution["low"] += count
            elif result_type == "failure":
                distribution["medium"] += count
            elif result_type == "error":
                distribution["high"] += count

        return distribution

    async def _get_time_series_data(
        self, base_conditions: List, group_by: str, start_dt: datetime, end_dt: datetime
    ) -> List[Dict[str, Any]]:
        """获取时间序列数据"""
        # 根据分组方式确定时间格式
        if group_by == "hour":
            date_format = "%Y-%m-%d %H:00:00"
            date_trunc = "hour"
        elif group_by == "day":
            date_format = "%Y-%m-%d"
            date_trunc = "day"
        elif group_by == "week":
            date_format = "%Y-%W"
            date_trunc = "week"
        elif group_by == "month":
            date_format = "%Y-%m"
            date_trunc = "month"
        else:
            date_format = "%Y-%m-%d"
            date_trunc = "day"

        # 使用数据库的日期截断函数
        stmt = (
            select(
                func.date_trunc(date_trunc, self.audit_log_model.created_at).label("time_period"),
                func.count(self.audit_log_model.id).label("total"),
                func.sum(func.case((self.audit_log_model.result == "success", 1), else_=0)).label(
                    "success"
                ),
                func.sum(
                    func.case(
                        (self.audit_log_model.result.in_(["failure", "error"]), 1),
                        else_=0,
                    )
                ).label("failed"),
                func.sum(func.case((self.audit_log_model.result == "error", 1), else_=0)).label(
                    "high_risk"
                ),
            )
            .where(and_(*base_conditions))
            .group_by("time_period")
            .order_by("time_period")
        )

        result = await self.session.execute(stmt)
        return [
            {
                "time": row[0].strftime(date_format),
                "total": row[1],
                "success": row[2],
                "failed": row[3],
                "high_risk": row[4],
            }
            for row in result.fetchall()
        ]

    async def _generate_export_file(
        self, logs: List, format: str, include_details: bool
    ) -> tuple[str, str, int]:
        """生成导出文件"""
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")

        if format == "csv":
            return await self._generate_csv_file(logs, include_details, timestamp)
        elif format == "json":
            return await self._generate_json_file(logs, include_details, timestamp)
        elif format == "excel":
            return await self._generate_excel_file(logs, include_details, timestamp)
        else:
            raise ValidationError(f"不支持的导出格式: {format}")

    async def _generate_csv_file(
        self, logs: List, include_details: bool, timestamp: str
    ) -> tuple[str, str, int]:
        """生成CSV文件"""
        output = io.StringIO()

        # 定义字段
        fieldnames = [
            "日志ID",
            "租户ID",
            "用户ID",
            "用户名",
            "操作类型",
            "资源类型",
            "资源ID",
            "描述",
            "IP地址",
            "状态",
            "风险级别",
            "创建时间",
        ]

        if include_details:
            fieldnames.append("详细信息")

        writer = csv.DictWriter(output, fieldnames=fieldnames)
        writer.writeheader()

        for log in logs:
            # 获取用户名
            username = ""
            if log.user_id:
                user = await self._get_user_by_id(log.user_id)
                if user:
                    username = user.username

            row = {
                "日志ID": str(log.id),
                "租户ID": log.tenant_id,
                "用户ID": log.user_id or "",
                "用户名": username,
                "操作类型": log.action,
                "资源类型": log.resource_type,
                "资源ID": log.resource_id or "",
                "描述": log.error_message or "",
                "IP地址": log.ip_address or "",
                "状态": log.result,
                "风险级别": self._calculate_risk_level(log),
                "创建时间": log.created_at.isoformat(),
            }

            if include_details:
                row["详细信息"] = json.dumps(log.details, ensure_ascii=False) if log.details else ""

            writer.writerow(row)

        content = output.getvalue()
        file_name = f"audit_logs_{timestamp}.csv"
        file_size = len(content.encode("utf-8"))

        return content, file_name, file_size

    async def _generate_json_file(
        self, logs: List, include_details: bool, timestamp: str
    ) -> tuple[str, str, int]:
        """生成JSON文件"""
        data = []

        for log in logs:
            # 获取用户名
            username = None
            if log.user_id:
                user = await self._get_user_by_id(log.user_id)
                if user:
                    username = user.username

            log_data = {
                "log_id": str(log.id),
                "tenant_id": log.tenant_id,
                "user_id": log.user_id,
                "username": username,
                "action": log.action,
                "resource_type": log.resource_type,
                "resource_id": log.resource_id,
                "description": log.error_message,
                "ip_address": log.ip_address,
                "user_agent": log.user_agent,
                "status": log.result,
                "risk_level": self._calculate_risk_level(log),
                "created_at": log.created_at.isoformat(),
            }

            if include_details and log.details:
                log_data["details"] = log.details

            data.append(log_data)

        content = json.dumps(data, ensure_ascii=False, indent=2)
        file_name = f"audit_logs_{timestamp}.json"
        file_size = len(content.encode("utf-8"))

        return content, file_name, file_size

    async def _generate_excel_file(
        self, logs: List, include_details: bool, timestamp: str
    ) -> tuple[str, str, int]:
        """生成Excel文件"""
        # TODO: 实现Excel文件生成
        # 需要安装openpyxl或xlsxwriter库
        # 这里暂时返回CSV格式
        return await self._generate_csv_file(logs, include_details, timestamp)

    def _calculate_risk_level(self, log) -> str:
        """计算风险级别"""
        # 基于操作结果和操作类型计算风险级别
        if log.result == "error":
            return "high"
        elif log.result == "failure":
            return "medium"
        elif log.action in ["DELETE", "ADMIN_LOGIN", "PERMISSION_CHANGE"]:
            return "medium"
        else:
            return "low"
