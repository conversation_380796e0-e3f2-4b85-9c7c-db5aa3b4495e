"""
系统初始化引导程序

负责创建系统启动所需的默认角色、权限和超级管理员用户
解决系统冷启动时的"鸡生蛋"问题
"""

import secrets
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from sqlalchemy import and_, select
from sqlalchemy.ext.asyncio import AsyncSession

from domain_common.models import CommonStatus
from domain_common.models.constants import PLATFORM_TENANT_ID, RoleType, UserGroupType
from domain_common.models.iam_models import (
    Permission,
    Role,
    RolePermission,
    User,
    UserGroup,
    UserRole,
)
from domain_common.security import SecurityUtils


class SystemBootstrap:
    """系统初始化引导类

    负责系统首次启动时的初始化工作：
    1. 创建平台级系统角色
    2. 创建系统权限
    3. 创建默认超级管理员用户
    4. 创建系统用户组
    """

    def __init__(self, session: AsyncSession, security_utils: SecurityUtils):
        self.session = session
        self.security_utils = security_utils

    async def initialize_system(
        self,
        admin_username: str = "platform_admin",
        admin_email: str = "<EMAIL>",
        admin_password: Optional[str] = "123456",
    ) -> Dict[str, Any]:
        """
        系统完整初始化

        Args:
            admin_username: 超级管理员用户名
            admin_email: 超级管理员邮箱
            admin_password: 超级管理员密码（如果为空则自动生成）

        Returns:
            初始化结果信息
        """

        # 检查是否已经初始化
        if await self._is_system_initialized():
            return {}

        result = {
            "initialized_at": datetime.now().isoformat(),
            "admin_user": {},
            "roles": [],
            "permissions": [],
            "groups": [],
            "generated_password": None,
        }

        try:
            # 1. 创建系统权限
            permissions = await self._create_system_permissions()
            result["permissions"] = [p.permission_code for p in permissions]

            # 2. 创建系统角色
            roles = await self._create_system_roles()
            result["roles"] = [r.role_code for r in roles]

            # 3. 为角色分配权限
            await self._assign_permissions_to_roles(roles, permissions)

            # 4. 创建系统用户组
            groups = await self._create_system_groups()
            result["groups"] = [g.group_code for g in groups]

            # 5. 创建超级管理员用户
            if not admin_password:
                admin_password = self._generate_secure_password()
                result["generated_password"] = admin_password

            admin_user = await self._create_super_admin_user(
                admin_username, admin_email, admin_password
            )
            result["admin_user"] = {
                "user_id": admin_user.user_id,
                "username": admin_user.username,
                "email": admin_user.email,
            }

            # 6. 为超级管理员分配角色
            platform_super_admin_role = next(
                r for r in roles if r.role_type == RoleType.PLATFORM_SUPER_ADMIN
            )
            await self._assign_role_to_user(admin_user, platform_super_admin_role)

            # 7. 将超级管理员加入系统管理员组
            admin_group = next(g for g in groups if g.group_code == "PLATFORM_ADMINS")
            await self._add_user_to_group(admin_user, admin_group)

            await self.session.commit()

            return result

        except Exception as e:
            await self.session.rollback()
            raise RuntimeError(f"系统初始化失败: {str(e)}")

    async def _is_system_initialized(self) -> bool:
        """检查系统是否已经初始化"""
        stmt = select(Role).where(
            and_(
                Role.tenant_id == PLATFORM_TENANT_ID,
                Role.role_type == RoleType.PLATFORM_SUPER_ADMIN,
                Role.is_system_role == True,
            )
        )
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none() is not None

    async def _create_system_permissions(self) -> List[Permission]:
        """创建系统权限"""
        system_permissions = [
            # 平台管理权限
            {
                "permission_code": "platform:manage",
                "permission_name": "平台管理",
                "resource": "platform",
                "action": "manage",
                "description": "平台级别的管理权限",
            },
            {
                "permission_code": "tenant:create",
                "permission_name": "创建租户",
                "resource": "tenant",
                "action": "create",
                "description": "创建新租户的权限",
            },
            {
                "permission_code": "tenant:manage",
                "permission_name": "管理租户",
                "resource": "tenant",
                "action": "manage",
                "description": "管理租户的权限",
            },
            {
                "permission_code": "tenant:delete",
                "permission_name": "删除租户",
                "resource": "tenant",
                "action": "delete",
                "description": "删除租户的权限",
            },
            # 用户管理权限
            {
                "permission_code": "user:create",
                "permission_name": "创建用户",
                "resource": "user",
                "action": "create",
                "description": "创建用户的权限",
            },
            {
                "permission_code": "user:manage",
                "permission_name": "管理用户",
                "resource": "user",
                "action": "manage",
                "description": "管理用户的权限",
            },
            {
                "permission_code": "user:delete",
                "permission_name": "删除用户",
                "resource": "user",
                "action": "delete",
                "description": "删除用户的权限",
            },
            # 角色权限管理
            {
                "permission_code": "role:create",
                "permission_name": "创建角色",
                "resource": "role",
                "action": "create",
                "description": "创建角色的权限",
            },
            {
                "permission_code": "role:manage",
                "permission_name": "管理角色",
                "resource": "role",
                "action": "manage",
                "description": "管理角色的权限",
            },
            {
                "permission_code": "permission:manage",
                "permission_name": "管理权限",
                "resource": "permission",
                "action": "manage",
                "description": "管理权限的权限",
            },
            # 用户组管理权限
            {
                "permission_code": "group:create",
                "permission_name": "创建用户组",
                "resource": "group",
                "action": "create",
                "description": "创建用户组的权限",
            },
            {
                "permission_code": "group:manage",
                "permission_name": "管理用户组",
                "resource": "group",
                "action": "manage",
                "description": "管理用户组的权限",
            },
            # 系统配置权限
            {
                "permission_code": "system:config",
                "permission_name": "系统配置",
                "resource": "system",
                "action": "config",
                "description": "系统配置管理权限",
            },
            {
                "permission_code": "audit:view",
                "permission_name": "查看审计日志",
                "resource": "audit",
                "action": "view",
                "description": "查看审计日志的权限",
            },
        ]

        permissions = []
        for perm_data in system_permissions:
            permission = Permission(
                permission_id=str(uuid.uuid4()),
                tenant_id=None,  # 系统级权限不属于任何租户
                permission_name=perm_data["permission_name"],
                permission_code=perm_data["permission_code"],
                resource=perm_data["resource"],
                action=perm_data["action"],
                description=perm_data["description"],
                level=1,
                is_inheritable=True,
                status=CommonStatus.ACTIVE,
            )
            permissions.append(permission)
            self.session.add(permission)

        return permissions

    async def _create_system_roles(self) -> List[Role]:
        """创建系统角色"""
        system_roles = [
            {
                "role_code": "PLATFORM_SUPER_ADMIN",
                "role_name": "平台超级管理员",
                "role_type": RoleType.PLATFORM_SUPER_ADMIN,
                "description": "拥有对整个平台的完全控制权限，包括所有租户的管理",
                "level": 1,
            },
            {
                "role_code": "PLATFORM_ADMIN",
                "role_name": "平台管理员",
                "role_type": RoleType.PLATFORM_ADMIN,
                "description": "平台级管理员，负责租户管理和平台配置",
                "level": 2,
            },
            {
                "role_code": "TENANT_SUPER_ADMIN_TEMPLATE",
                "role_name": "租户超级管理员模板",
                "role_type": RoleType.TENANT_SUPER_ADMIN,
                "description": "租户超级管理员角色模板，用于为新租户创建管理员角色",
                "level": 1,
            },
        ]

        roles = []
        for role_data in system_roles:
            role = Role(
                role_id=str(uuid.uuid4()),
                tenant_id=PLATFORM_TENANT_ID,  # 平台级角色
                role_name=role_data["role_name"],
                role_code=role_data["role_code"],
                role_type=role_data["role_type"],
                description=role_data["description"],
                level=role_data["level"],
                is_platform_role=True,
                is_system_role=True,
                status=CommonStatus.ACTIVE,
            )
            roles.append(role)
            self.session.add(role)

        return roles

    async def _assign_permissions_to_roles(self, roles: List[Role], permissions: List[Permission]):
        """为角色分配权限"""

        # 平台超级管理员拥有所有权限
        platform_super_admin = next(
            r for r in roles if r.role_type == RoleType.PLATFORM_SUPER_ADMIN
        )
        for permission in permissions:
            role_permission = RolePermission(
                tenant_id=PLATFORM_TENANT_ID,
                role_id=platform_super_admin.role_id,
                permission_id=permission.permission_id,
                status=CommonStatus.ACTIVE,
            )
            self.session.add(role_permission)

        # 平台管理员拥有租户管理相关权限
        platform_admin = next(r for r in roles if r.role_type == RoleType.PLATFORM_ADMIN)
        admin_permissions = [
            "tenant:create",
            "tenant:manage",
            "user:create",
            "user:manage",
            "role:create",
            "role:manage",
            "group:create",
            "group:manage",
            "audit:view",
        ]
        for permission in permissions:
            if permission.permission_code in admin_permissions:
                role_permission = RolePermission(
                    tenant_id=PLATFORM_TENANT_ID,
                    role_id=platform_admin.role_id,
                    permission_id=permission.permission_id,
                    status=CommonStatus.ACTIVE,
                )
                self.session.add(role_permission)

    async def _create_system_groups(self) -> List[UserGroup]:
        """创建系统用户组"""
        system_groups = [
            {
                "group_code": "PLATFORM_ADMINS",
                "group_name": "平台管理员组",
                "group_type": UserGroupType.ROLE_GROUP,
                "description": "平台级管理员用户组",
            },
            {
                "group_code": "SYSTEM_OPERATORS",
                "group_name": "系统运维组",
                "group_type": UserGroupType.ROLE_GROUP,
                "description": "系统运维人员用户组",
            },
        ]

        groups = []
        for group_data in system_groups:
            group = UserGroup(
                group_id=str(uuid.uuid4()),
                tenant_id=PLATFORM_TENANT_ID,  # 系统级用户组
                group_name=group_data["group_name"],
                group_code=group_data["group_code"],
                group_type=group_data["group_type"],
                description=group_data["description"],
                level=1,
                status=CommonStatus.ACTIVE,
            )
            groups.append(group)
            self.session.add(group)

        return groups

    async def _create_super_admin_user(self, username: str, email: str, password: str) -> User:
        """创建超级管理员用户"""

        # 生成密码哈希
        salt = secrets.token_hex(32)
        password_hash = self.security_utils.hash_password(password)

        user = User(
            user_id=str(uuid.uuid4()),
            tenant_id=PLATFORM_TENANT_ID,  # 平台级用户
            username=username,
            email=email,
            password_hash=password_hash,
            salt=salt,
            nickname="平台超级管理员",
            status=CommonStatus.ACTIVE,
            profile={
                "is_super_admin": True,
                "created_by_system": True,
                "description": "系统初始化创建的超级管理员",
            },
        )

        self.session.add(user)
        return user

    async def _assign_role_to_user(self, user: User, role: Role):
        """为用户分配角色"""
        user_role = UserRole(
            tenant_id=PLATFORM_TENANT_ID,
            user_id=user.user_id,
            role_id=role.role_id,
            assignment_type="permanent",
            status=CommonStatus.ACTIVE,
        )
        self.session.add(user_role)

    async def _add_user_to_group(self, user: User, group: UserGroup):
        """将用户添加到用户组"""
        from domain_common.models.iam_models.relations import UserGroupMember

        member = UserGroupMember(
            tenant_id=PLATFORM_TENANT_ID,
            user_id=user.user_id,
            group_id=group.group_id,
            assignment_type="permanent",
            status=CommonStatus.ACTIVE,
        )
        self.session.add(member)

    def _generate_secure_password(self, length: int = 16) -> str:
        """生成安全密码"""
        import string

        # 确保密码包含各种字符类型
        chars = string.ascii_letters + string.digits + "!@#$%^&*"
        password = "".join(secrets.choice(chars) for _ in range(length))

        # 确保至少包含一个大写字母、小写字母、数字和特殊字符
        if not any(c.isupper() for c in password):
            password = password[:-1] + secrets.choice(string.ascii_uppercase)
        if not any(c.islower() for c in password):
            password = password[:-1] + secrets.choice(string.ascii_lowercase)
        if not any(c.isdigit() for c in password):
            password = password[:-1] + secrets.choice(string.digits)
        if not any(c in "!@#$%^&*" for c in password):
            password = password[:-1] + secrets.choice("!@#$%^&*")

        return password


# 使用示例
async def bootstrap_system(session: AsyncSession) -> Dict[str, Any]:
    """系统初始化入口函数"""
    bootstrap = SystemBootstrap(session)

    result = await bootstrap.initialize_system(
        admin_username="platform_admin", admin_email="<EMAIL>"
    )

    return result
