"""
消息代理配置

支持Redis、RabbitMQ等消息代理的统一配置
"""

from pydantic import BaseModel, ConfigDict, Field


class MessageBrokerConfig(BaseModel):
    """消息代理配置类"""
    
    # Redis消息代理配置
    redis_url: str = Field(
        default="redis://localhost:6379/0", 
        description="Redis消息代理URL"
    )
    
    # 主题/队列配置
    user_events_topic: str = Field(
        default="user_events", 
        description="用户事件主题名称"
    )
    
    # 消息格式配置
    message_format: str = Field(
        default="binary_v1", 
        description="消息格式版本"
    )
    
    # 连接配置
    connection_timeout: int = Field(
        default=30, 
        description="连接超时时间（秒）"
    )
    
    retry_attempts: int = Field(
        default=3, 
        description="重试次数"
    )
    
    model_config = ConfigDict(
        validate_assignment=True,
        validate_default=True,
        extra="allow",
    )


class WorkerConfig(BaseModel):
    """Worker配置类"""
    
    # 并发配置
    concurrency: int = Field(
        default=3, 
        description="消费者并发数量"
    )
    
    # 处理配置
    max_retries: int = Field(
        default=3, 
        description="消息处理最大重试次数"
    )
    
    retry_delay: int = Field(
        default=5, 
        description="重试延迟时间（秒）"
    )
    
    # 监控配置
    enable_metrics: bool = Field(
        default=True, 
        description="是否启用指标监控"
    )
    
    log_level: str = Field(
        default="INFO", 
        description="日志级别"
    )
    
    model_config = ConfigDict(
        validate_assignment=True,
        validate_default=True,
        extra="allow",
    )
