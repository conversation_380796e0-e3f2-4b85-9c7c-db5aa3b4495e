# ThingsMore 项目文档

## 一、项目概述
ThingsMore 是一个基于 Python 的微服务架构项目，采用 FastAPI 框架构建。项目包含多个服务模块，提供认证中心、配置管理等功能，并集成了多种数据库和中间件支持。

## 二、目录结构说明

### 1. common_service（通用服务模块）
- `app_builder`：应用构建器，提供默认应用构建逻辑
- `exceptions`：
  - `custom_exceptions.py`：自定义异常类
  - `exception_handlers.py`：异常处理器
- `middlewares`：各种中间件实现
  - CORS、错误处理、日志记录、指标收集、限流、安全、统一响应等中间件

### 2. configs（配置文件）
环境相关的配置文件：
- `config_dev.json`：开发环境配置
- `config_prd.json`：生产环境配置

### 3. infrastructure（基础设施层）
- `config_models`：配置模型定义
  - 应用、框架、服务和存储的配置模型
- `core`：核心功能
  - 依赖注入容器、上下文管理、装饰器、日志等
- `storages`：存储相关
  - 数据库连接器、MQ 客户端、各种存储库实现
- `utils`：实用工具
  - 加密、日期时间、缓存键序列化、雪花 ID 生成等

### 4. logs（日志目录）
按日志级别和类型分类的日志文件

### 5. model_lib（模型库）
- `models`：数据模型定义
- `repositories`：存储库接口
- `schemas`：API 响应模型

### 6. services（业务服务）
- `auth_center`：认证中心服务
  - 应用工厂、常量定义、存储访问器等
- `config_management`：配置管理服务
  - API 路由、服务层、测试等

### 7. scripts（脚本）
- `quality`：代码质量相关脚本
  - 格式化、lint、类型检查等
- `run`：运行脚本

### 8. 其他文件
- 项目配置：`pyproject.toml`, `setup.py`, `pytest.ini`, `mypy.ini`
- 依赖管理：`requirements/*.txt`
- 文档：`架构.md`

## 三、开发指南

### 1. 新成员上手步骤
**环境准备**
- 安装 Python 3.12+
- 安装依赖：`pip install -r requirements/dev.txt`
- 配置 IDE 支持（推荐 VS Code 或 PyCharm）

**项目结构理解**
- 阅读本 README 和 `架构.md`
- 重点关注 `infrastructure` 和 `services` 目录

**开发流程**
- 使用 `scripts/quality` 下的脚本保证代码质量
- 遵循现有项目的分层架构
- 新功能开发顺序：
  - 模型定义 → 存储库接口 → 服务层 → API 路由

**测试**
- 单元测试：`pytest`
- 类型检查：`mypy`
- 代码风格：`flake8`

**运行服务**
- 使用 `scripts/run` 下的脚本启动服务
- 或直接运行各服务的 `main.py`

### 2. 编码规范
**分层架构**
- 严格遵循分层架构，各层之间通过接口通信
- 避免跨层直接调用

**依赖注入**
- 使用 `infrastructure/core/containers` 中的容器管理依赖
- 通过 `@inject` 装饰器注入依赖

**异常处理**
- 使用 `common_service/exceptions` 中的异常类
- 通过中间件统一处理异常

**日志记录**
- 使用 `infrastructure/core/logging` 中的日志工具
- 按业务、错误、信息等分类记录日志

**配置管理**
- 配置应定义在 `configs` 目录
- 通过 `config_models` 中的模型加载配置

**docker构建**
- 构建命令应该在项目的根目录中运行
- 项目构建命令: ` docker build -f services/example_service/Dockerfile -t example-service .`
- docker运行命令: `docker run -p 8082:8082 example-service`

## 四、扩展开发

### 1. 添加新服务
在 `services` 下创建新服务目录，参考现有服务结构：
新服务/
├─ app/ # 应用层（API 路由等）
├─ core/ # 核心逻辑
├─ services/ # 服务层
└─ tests/ # 测试

### 2. 添加新存储
1. 在 `infrastructure/config_models/storages` 中添加配置模型
2. 在 `infrastructure/storages` 中实现连接器和存储库
3. 在容器中注册新存储

## 五、贡献指南
提交 PR 前确保：
- 通过所有测试
- 代码格式化
- 类型检查通过
- 更新相关文档
- 重大变更应先创建 issue 讨论

## 六、联系信息
如有问题，请联系项目维护团队。