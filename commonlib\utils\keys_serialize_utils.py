import hashlib
import json
from typing import Any, Callable, Dict, TypeVar

from commonlib.utils.func_toolkit import fullname

T = TypeVar("T")


class SafeCacheKeyGenerator:
    """安全的缓存键生成器"""

    @classmethod
    def pack_mongo_arguments(cls, arg_obj: Any, kwargs_obj: Any) -> Dict[str, str]:
        if arg_obj and hasattr(arg_obj[0], "__name__"):
            arg_obj = arg_obj[0:]

        return {
            "args": cls.safe_serialize(arg_obj),
            "kwargs": cls.safe_serialize(kwargs_obj),
        }

    @staticmethod
    def safe_serialize(obj: Any) -> Any:
        """递归处理不可序列化对象"""
        if isinstance(obj, (int, float, str, bool, type(None))):
            return obj
        elif isinstance(obj, (list, tuple)):
            return [SafeCacheKeyGenerator.safe_serialize(item) for item in obj]
        elif isinstance(obj, dict):
            return {k: SafeCacheKeyGenerator.safe_serialize(v) for k, v in obj.items()}
        else:
            # 处理不可序列化对象
            return f"{obj.__class__.__name__}"

    @classmethod
    def make_key(cls, func: Callable, need_hash: bool, *args, **kwargs) -> str:
        """生成稳定的缓存键"""
        # 序列化参数
        safe_args = cls.safe_serialize(args)
        safe_kwargs = cls.safe_serialize(kwargs)

        # 添加函数标识
        func_id = fullname(func)

        arg_str = json.dumps(
            {"args": safe_args, "kwargs": safe_kwargs}, sort_keys=True, default=str
        )
        arg_str = arg_str.replace(".", ":")
        if need_hash:
            # 生成哈希
            hash_key = hashlib.md5(arg_str.encode()).hexdigest()
            return f"{func_id}:{hash_key}"
        return f"{func_id}:{arg_str}"

    @classmethod
    def build_hash_key(cls, *args, **kwargs) -> str:
        """生成稳定的缓存键"""
        # 序列化参数
        safe_args = cls.safe_serialize(args)
        safe_kwargs = cls.safe_serialize(kwargs)
        arg_str = json.dumps(
            {"args": safe_args, "kwargs": safe_kwargs}, sort_keys=True, default=str
        )
        return hashlib.md5(arg_str.encode()).hexdigest()
